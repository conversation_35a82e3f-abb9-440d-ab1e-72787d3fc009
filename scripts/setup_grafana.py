#!/usr/bin/env python3
"""
Grafana配置脚本
自动配置ClickHouse数据源和创建仪表盘
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class GrafanaManager:
    """Grafana管理器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
        
    def wait_for_grafana(self, max_wait=60) -> bool:
        """等待Grafana启动"""
        logger.info("等待Grafana启动...")
        
        for i in range(max_wait):
            try:
                response = self.session.get(f"{self.base_url}/api/health", timeout=5)
                if response.status_code == 200:
                    logger.info("✓ Grafana已启动")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        logger.error("✗ Grafana启动超时")
        return False
    
    def install_clickhouse_plugin(self) -> bool:
        """安装ClickHouse插件"""
        logger.info("检查ClickHouse插件...")
        
        # 检查插件是否已安装
        try:
            response = self.session.get(f"{self.base_url}/api/plugins/grafana-clickhouse-datasource")
            if response.status_code == 200:
                logger.info("✓ ClickHouse插件已安装")
                return True
        except requests.exceptions.RequestException:
            pass
        
        # 尝试安装插件
        logger.info("安装ClickHouse插件...")
        try:
            install_data = {
                "pluginId": "grafana-clickhouse-datasource",
                "version": ""
            }
            response = self.session.post(
                f"{self.base_url}/api/plugins/grafana-clickhouse-datasource/install",
                json=install_data
            )
            
            if response.status_code in [200, 201]:
                logger.info("✓ ClickHouse插件安装成功")
                return True
            else:
                logger.warning(f"插件安装响应: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"插件安装失败: {e}")
            return False
    
    def create_clickhouse_datasource(self) -> bool:
        """创建ClickHouse数据源"""
        logger.info("创建ClickHouse数据源...")
        
        # 检查数据源是否已存在
        try:
            response = self.session.get(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code == 200:
                logger.info("✓ ClickHouse数据源已存在")
                return True
        except requests.exceptions.RequestException:
            pass
        
        # 创建数据源
        datasource_config = {
            "name": "ClickHouse-Financial",
            "type": "grafana-clickhouse-datasource",
            "url": "http://localhost:8123",
            "access": "proxy",
            "database": "financial_metrics",
            "user": "default",
            "basicAuth": False,
            "isDefault": True,
            "jsonData": {
                "defaultDatabase": "financial_metrics",
                "port": 8123,
                "server": "localhost",
                "username": "default",
                "protocol": "http",
                "secure": False,
                "tlsSkipVerify": False,
                "dialTimeout": 10,
                "queryTimeout": 60,
                "idleTimeout": 60
            },
            "secureJsonData": {}
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                logger.info("✓ ClickHouse数据源创建成功")
                return True
            else:
                logger.error(f"数据源创建失败: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"数据源创建失败: {e}")
            return False
    
    def test_datasource_connection(self) -> bool:
        """测试数据源连接"""
        logger.info("测试ClickHouse数据源连接...")
        
        try:
            # 获取数据源ID
            response = self.session.get(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code != 200:
                logger.error("无法找到ClickHouse数据源")
                return False
            
            datasource = response.json()
            datasource_id = datasource['id']
            
            # 测试连接
            test_response = self.session.post(f"{self.base_url}/api/datasources/{datasource_id}/health")
            
            if test_response.status_code == 200:
                logger.info("✓ ClickHouse数据源连接测试成功")
                return True
            else:
                logger.error(f"数据源连接测试失败: {test_response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"数据源连接测试失败: {e}")
            return False
    
    def get_datasources(self) -> List[Dict]:
        """获取所有数据源"""
        try:
            response = self.session.get(f"{self.base_url}/api/datasources")
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"获取数据源失败: {response.status_code}")
                return []
        except requests.exceptions.RequestException as e:
            logger.error(f"获取数据源失败: {e}")
            return []
    
    def create_folder(self, folder_name: str) -> Optional[int]:
        """创建文件夹"""
        logger.info(f"创建文件夹: {folder_name}")
        
        # 检查文件夹是否已存在
        try:
            response = self.session.get(f"{self.base_url}/api/folders")
            if response.status_code == 200:
                folders = response.json()
                for folder in folders:
                    if folder['title'] == folder_name:
                        logger.info(f"✓ 文件夹 '{folder_name}' 已存在")
                        return folder['id']
        except requests.exceptions.RequestException:
            pass
        
        # 创建文件夹
        folder_config = {
            "title": folder_name,
            "uid": folder_name.lower().replace(' ', '-')
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/folders",
                json=folder_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                folder_data = response.json()
                logger.info(f"✓ 文件夹 '{folder_name}' 创建成功")
                return folder_data['id']
            else:
                logger.error(f"文件夹创建失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"文件夹创建失败: {e}")
            return None

def main():
    """主函数"""
    print("=== Grafana配置脚本 ===\n")
    
    grafana = GrafanaManager()
    
    # 1. 等待Grafana启动
    if not grafana.wait_for_grafana():
        return False
    
    # 2. 安装ClickHouse插件
    if not grafana.install_clickhouse_plugin():
        logger.warning("ClickHouse插件安装失败，尝试继续...")
    
    # 3. 创建ClickHouse数据源
    if not grafana.create_clickhouse_datasource():
        return False
    
    # 4. 测试数据源连接
    if not grafana.test_datasource_connection():
        logger.warning("数据源连接测试失败，但继续执行...")
    
    # 5. 创建文件夹
    folder_id = grafana.create_folder("Financial Metrics")
    
    # 6. 显示配置摘要
    datasources = grafana.get_datasources()
    
    print("\n=== 配置摘要 ===")
    print(f"Grafana URL: http://localhost:3000")
    print(f"用户名: admin")
    print(f"密码: admin123")
    print(f"数据源数量: {len(datasources)}")
    
    for ds in datasources:
        print(f"  - {ds['name']} ({ds['type']})")
    
    if folder_id:
        print(f"文件夹: Financial Metrics (ID: {folder_id})")
    
    print("\n✓ Grafana配置完成！")
    print("下一步: 创建仪表盘")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
