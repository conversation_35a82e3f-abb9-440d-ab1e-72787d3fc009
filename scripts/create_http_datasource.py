#!/usr/bin/env python3
"""
创建HTTP数据源连接ClickHouse
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HTTPDataSourceCreator:
    """HTTP数据源创建器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def create_http_datasource(self) -> bool:
        """创建HTTP数据源"""
        logger.info("创建HTTP数据源连接ClickHouse...")
        
        # 删除现有数据源
        try:
            self.session.delete(f"{self.base_url}/api/datasources/name/ClickHouse-HTTP")
        except:
            pass
        
        datasource_config = {
            "name": "ClickHouse-HTTP",
            "type": "grafana-infinity-datasource",
            "url": "http://127.0.0.1:8123",
            "access": "proxy",
            "basicAuth": False,
            "isDefault": True,
            "jsonData": {
                "url": "http://127.0.0.1:8123",
                "timeout": 30
            }
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                logger.info("✓ HTTP数据源创建成功")
                return True
            else:
                logger.error(f"HTTP数据源创建失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"HTTP数据源创建失败: {e}")
            return False
    
    def create_simple_datasource(self) -> bool:
        """创建简单的JSON数据源"""
        logger.info("创建简单JSON数据源...")
        
        # 删除现有数据源
        try:
            self.session.delete(f"{self.base_url}/api/datasources/name/ClickHouse-Simple")
        except:
            pass
        
        datasource_config = {
            "name": "ClickHouse-Simple",
            "type": "simpod-json-datasource",
            "url": "http://127.0.0.1:8123",
            "access": "proxy",
            "basicAuth": False,
            "isDefault": True,
            "jsonData": {}
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                logger.info("✓ 简单JSON数据源创建成功")
                return True
            else:
                logger.error(f"简单JSON数据源创建失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"简单JSON数据源创建失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 创建替代数据源 ===\n")
    
    creator = HTTPDataSourceCreator()
    
    # 尝试创建HTTP数据源
    if creator.create_http_datasource():
        print("✓ HTTP数据源创建成功")
    else:
        print("✗ HTTP数据源创建失败")
    
    # 尝试创建简单JSON数据源
    if creator.create_simple_datasource():
        print("✓ 简单JSON数据源创建成功")
    else:
        print("✗ 简单JSON数据源创建失败")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
