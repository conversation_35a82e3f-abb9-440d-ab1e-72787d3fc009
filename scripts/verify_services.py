#!/usr/bin/env python3
"""
服务验证脚本
验证ClickHouse和Grafana服务是否正常运行
"""

import requests
import sys
import time
from typing import <PERSON><PERSON>

def check_clickhouse() -> Tuple[bool, str]:
    """检查ClickHouse服务状态"""
    try:
        # 测试ping接口
        response = requests.get("http://localhost:8123/ping", timeout=5)
        if response.status_code == 200 and response.text.strip() == "Ok.":
            # 测试查询接口
            query_response = requests.get(
                "http://localhost:8123/?query=SELECT 1", 
                timeout=5
            )
            if query_response.status_code == 200:
                return True, "ClickHouse服务正常"
            else:
                return False, f"ClickHouse查询失败: {query_response.status_code}"
        else:
            return False, f"ClickHouse ping失败: {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, f"ClickHouse连接失败: {str(e)}"

def check_grafana() -> <PERSON><PERSON>[bool, str]:
    """检查Grafana服务状态"""
    try:
        response = requests.get("http://localhost:3000/api/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("database") == "ok":
                return True, f"Grafana服务正常 (版本: {data.get('version', 'unknown')})"
            else:
                return False, f"Grafana数据库状态异常: {data}"
        else:
            return False, f"Grafana健康检查失败: {response.status_code}"
    except requests.exceptions.RequestException as e:
        return False, f"Grafana连接失败: {str(e)}"

def main():
    """主函数"""
    print("=== 金融指标系统服务验证 ===\n")
    
    # 检查ClickHouse
    print("检查ClickHouse服务...")
    ch_ok, ch_msg = check_clickhouse()
    print(f"{'✓' if ch_ok else '✗'} {ch_msg}")
    
    # 检查Grafana
    print("\n检查Grafana服务...")
    gf_ok, gf_msg = check_grafana()
    print(f"{'✓' if gf_ok else '✗'} {gf_msg}")
    
    # 总结
    print(f"\n=== 验证结果 ===")
    if ch_ok and gf_ok:
        print("✓ 所有服务运行正常")
        print("\n访问地址:")
        print("- ClickHouse HTTP接口: http://localhost:8123")
        print("- Grafana仪表盘: http://localhost:3000 (admin/admin)")
        sys.exit(0)
    else:
        print("✗ 部分服务异常，请检查日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
