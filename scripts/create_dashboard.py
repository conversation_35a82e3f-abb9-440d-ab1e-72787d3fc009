#!/usr/bin/env python3
"""
创建Grafana金融指标仪表盘
"""

import requests
import json
import logging
from typing import Dict, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DashboardCreator:
    """仪表盘创建器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def create_financial_dashboard(self) -> bool:
        """创建金融指标仪表盘"""
        logger.info("创建金融指标仪表盘...")
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": "金融交易指标仪表盘",
                "tags": ["financial", "trading", "btc"],
                "timezone": "Asia/Shanghai",
                "refresh": "30s",
                "time": {
                    "from": "now-24h",
                    "to": "now"
                },
                "timepicker": {
                    "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]
                },
                "panels": [
                    # 价格走势图
                    {
                        "id": 1,
                        "title": "BTCUSDT 价格走势",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    close_price as "收盘价"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "",
                                    "axisPlacement": "auto",
                                    "barAlignment": 0,
                                    "drawStyle": "line",
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "hideFrom": {"legend": False, "tooltip": False, "vis": False},
                                    "lineInterpolation": "linear",
                                    "lineWidth": 2,
                                    "pointSize": 5,
                                    "scaleDistribution": {"type": "linear"},
                                    "showPoints": "never",
                                    "spanNulls": False,
                                    "stacking": {"group": "A", "mode": "none"},
                                    "thresholdsStyle": {"mode": "off"}
                                },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "red", "value": 80}
                                    ]
                                },
                                "unit": "currencyUSD"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "single", "sort": "none"}
                        }
                    },
                    # 成交量图
                    {
                        "id": 2,
                        "title": "成交量",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    total_volume as "总成交量",
                                    buy_volume as "买入量",
                                    sell_volume as "卖出量"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "",
                                    "axisPlacement": "auto",
                                    "barAlignment": 0,
                                    "drawStyle": "line",
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "hideFrom": {"legend": False, "tooltip": False, "vis": False},
                                    "lineInterpolation": "linear",
                                    "lineWidth": 1,
                                    "pointSize": 5,
                                    "scaleDistribution": {"type": "linear"},
                                    "showPoints": "never",
                                    "spanNulls": False,
                                    "stacking": {"group": "A", "mode": "none"},
                                    "thresholdsStyle": {"mode": "off"}
                                },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "red", "value": 80}
                                    ]
                                },
                                "unit": "short"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "single", "sort": "none"}
                        }
                    },
                    # K线图（OHLC）
                    {
                        "id": 3,
                        "title": "K线图 (OHLC)",
                        "type": "candlestick",
                        "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    open_price as "Open",
                                    high_price as "High", 
                                    low_price as "Low",
                                    close_price as "Close",
                                    total_volume as "Volume"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "candleStyle": "candles",
                                    "colorStrategy": "open-close",
                                    "mode": "candles+volume"
                                },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "red", "value": 80}
                                    ]
                                },
                                "unit": "currencyUSD"
                            }
                        }
                    },
                    # 统计指标面板
                    {
                        "id": 4,
                        "title": "交易统计",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 6, "x": 0, "y": 18},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    avg(close_price) as "平均价格",
                                    sum(total_volume) as "总成交量",
                                    sum(total_turnover) as "总成交额",
                                    sum(trade_count) as "交易笔数"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "short"
                            }
                        },
                        "options": {
                            "colorMode": "value",
                            "graphMode": "area",
                            "justifyMode": "auto",
                            "orientation": "auto",
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"],
                                "fields": ""
                            },
                            "textMode": "auto"
                        }
                    },
                    # 价差分析
                    {
                        "id": 5,
                        "title": "买卖价差",
                        "type": "timeseries",
                        "gridPos": {"h": 6, "w": 18, "x": 6, "y": 18},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    snapshot_time as time,
                                    spread as "价差",
                                    mid_price as "中间价"
                                FROM financial_metrics.orderbook_snapshots 
                                WHERE symbol = 'BTCUSDT'
                                AND snapshot_time >= $__fromTime AND snapshot_time <= $__toTime
                                ORDER BY snapshot_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "axisLabel": "",
                                    "axisPlacement": "auto",
                                    "barAlignment": 0,
                                    "drawStyle": "line",
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "hideFrom": {"legend": False, "tooltip": False, "vis": False},
                                    "lineInterpolation": "linear",
                                    "lineWidth": 1,
                                    "pointSize": 5,
                                    "scaleDistribution": {"type": "linear"},
                                    "showPoints": "never",
                                    "spanNulls": False,
                                    "stacking": {"group": "A", "mode": "none"},
                                    "thresholdsStyle": {"mode": "off"}
                                },
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None},
                                        {"color": "red", "value": 80}
                                    ]
                                },
                                "unit": "currencyUSD"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "single", "sort": "none"}
                        }
                    }
                ]
            },
            "folderId": 1,
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                logger.info("✓ 金融指标仪表盘创建成功")
                logger.info(f"仪表盘URL: {self.base_url}/d/{result.get('uid', '')}")
                return True
            else:
                logger.error(f"仪表盘创建失败: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"仪表盘创建失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 创建Grafana仪表盘 ===\n")
    
    creator = DashboardCreator()
    
    # 创建金融指标仪表盘
    if creator.create_financial_dashboard():
        print("\n✓ 仪表盘创建完成！")
        print("访问地址: http://localhost:3000")
        print("用户名: admin")
        print("密码: admin123")
        return True
    else:
        print("\n✗ 仪表盘创建失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
