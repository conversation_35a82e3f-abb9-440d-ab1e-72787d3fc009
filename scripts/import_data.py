#!/usr/bin/env python3
"""
完整的数据导入脚本
处理币安数据并导入到ClickHouse
"""

import sys
import argparse
from pathlib import Path
import logging
from datetime import datetime

# 导入自定义模块
from data_processor import BinanceDataProcessor
from clickhouse_importer import ClickHouseImporter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataImportPipeline:
    """数据导入流水线"""
    
    def __init__(self, data_dir: str = "tardis_shell_data"):
        self.processor = BinanceDataProcessor(data_dir)
        self.importer = ClickHouseImporter()
        self.stats = {
            'start_time': None,
            'end_time': None,
            'files_processed': 0,
            'total_trades': 0,
            'total_minutes': 0,
            'errors': []
        }
    
    def process_trades_file(self, filename: str, import_raw: bool = True, import_metrics: bool = True) -> bool:
        """处理单个交易文件"""
        try:
            logger.info(f"开始处理文件: {filename}")
            
            # 1. 加载数据
            trades_df = self.processor.load_trades_data(filename)
            if trades_df is None or len(trades_df) == 0:
                logger.warning(f"文件 {filename} 无数据")
                return False
            
            # 2. 清洗数据
            clean_df = self.processor.clean_trades_data(trades_df)
            
            # 3. 聚合到分钟级
            minute_df = self.processor.aggregate_to_minutes(clean_df)
            
            # 4. 准备导入数据
            success = True
            
            if import_raw:
                # 导入原始交易数据
                raw_data = self.processor.prepare_for_clickhouse(clean_df, 'raw_trades')
                if not self.importer.insert_dataframe(raw_data, 'financial_metrics.raw_trades'):
                    success = False
                    self.stats['errors'].append(f"原始数据导入失败: {filename}")
                else:
                    self.stats['total_trades'] += len(raw_data)
            
            if import_metrics:
                # 导入分钟级指标数据
                metrics_data = self.processor.prepare_for_clickhouse(minute_df, 'trade_metrics_minutely')
                if not self.importer.insert_dataframe(metrics_data, 'financial_metrics.trade_metrics_minutely'):
                    success = False
                    self.stats['errors'].append(f"指标数据导入失败: {filename}")
                else:
                    self.stats['total_minutes'] += len(metrics_data)
            
            if success:
                self.stats['files_processed'] += 1
                logger.info(f"✓ 文件 {filename} 处理完成")
            
            return success
            
        except Exception as e:
            error_msg = f"处理文件 {filename} 时发生错误: {e}"
            logger.error(error_msg)
            self.stats['errors'].append(error_msg)
            return False
    
    def import_all_trades(self, pattern: str = "*trades*.csv", import_raw: bool = True, import_metrics: bool = True) -> bool:
        """导入所有交易文件"""
        self.stats['start_time'] = datetime.now()
        
        # 查找所有交易文件
        data_dir = Path(self.processor.data_dir)
        trade_files = list(data_dir.glob(pattern))
        
        if not trade_files:
            logger.error(f"未找到匹配的文件: {pattern}")
            return False
        
        logger.info(f"找到 {len(trade_files)} 个交易文件")
        
        success_count = 0
        for file_path in trade_files:
            if self.process_trades_file(file_path.name, import_raw, import_metrics):
                success_count += 1
        
        self.stats['end_time'] = datetime.now()
        
        # 优化表
        if success_count > 0:
            if import_raw:
                self.importer.optimize_table('financial_metrics.raw_trades')
            if import_metrics:
                self.importer.optimize_table('financial_metrics.trade_metrics_minutely')
        
        return success_count == len(trade_files)
    
    def verify_import(self) -> bool:
        """验证导入结果"""
        logger.info("验证导入结果...")
        
        summary = self.importer.get_import_summary()
        
        all_good = True
        for table, info in summary.items():
            count = info['count']
            if count > 0:
                logger.info(f"✓ 表 {table}: {count} 条记录")
                
                # 显示样本数据
                if info['sample']:
                    logger.info(f"  样本数据: {info['sample'][0]}")
            else:
                logger.warning(f"✗ 表 {table}: 无数据")
                all_good = False
        
        return all_good
    
    def print_statistics(self) -> None:
        """打印统计信息"""
        print("\n" + "="*50)
        print("数据导入统计")
        print("="*50)
        
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            print(f"处理时间: {duration}")
        
        print(f"处理文件数: {self.stats['files_processed']}")
        print(f"导入交易记录: {self.stats['total_trades']:,}")
        print(f"导入分钟记录: {self.stats['total_minutes']:,}")
        
        if self.stats['errors']:
            print(f"\n错误数量: {len(self.stats['errors'])}")
            for error in self.stats['errors']:
                print(f"  - {error}")
        else:
            print("\n✓ 无错误")
        
        print("="*50)
    
    def cleanup(self) -> None:
        """清理资源"""
        if self.importer:
            self.importer.close()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='导入币安交易数据到ClickHouse')
    parser.add_argument('--pattern', default='*trades*.csv', help='文件匹配模式')
    parser.add_argument('--no-raw', action='store_true', help='跳过原始数据导入')
    parser.add_argument('--no-metrics', action='store_true', help='跳过指标数据导入')
    parser.add_argument('--clear', action='store_true', help='清空现有数据')
    
    args = parser.parse_args()
    
    pipeline = DataImportPipeline()
    
    try:
        # 清空数据（如果需要）
        if args.clear:
            logger.warning("清空现有数据...")
            if not args.no_raw:
                pipeline.importer.clear_table('financial_metrics.raw_trades')
            if not args.no_metrics:
                pipeline.importer.clear_table('financial_metrics.trade_metrics_minutely')
        
        # 执行导入
        import_raw = not args.no_raw
        import_metrics = not args.no_metrics
        
        success = pipeline.import_all_trades(
            pattern=args.pattern,
            import_raw=import_raw,
            import_metrics=import_metrics
        )
        
        # 验证结果
        pipeline.verify_import()
        
        # 打印统计
        pipeline.print_statistics()
        
        if success:
            print("\n✓ 数据导入完成！")
            sys.exit(0)
        else:
            print("\n✗ 数据导入部分失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("用户中断导入")
        sys.exit(1)
    except Exception as e:
        logger.error(f"导入过程发生错误: {e}")
        sys.exit(1)
    finally:
        pipeline.cleanup()

if __name__ == "__main__":
    main()
