#!/usr/bin/env python3
"""
修复Grafana数据源配置
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataSourceFixer:
    """数据源修复器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def delete_existing_datasource(self) -> bool:
        """删除现有数据源"""
        try:
            response = self.session.delete(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code in [200, 404]:
                logger.info("✓ 现有数据源已删除")
                return True
            else:
                logger.warning(f"删除数据源响应: {response.status_code}")
                return True  # 继续执行
        except Exception as e:
            logger.error(f"删除数据源失败: {e}")
            return False
    
    def create_fixed_datasource(self) -> bool:
        """创建修复的数据源"""
        logger.info("创建修复的ClickHouse数据源...")
        
        # 使用更简单的配置
        datasource_config = {
            "name": "ClickHouse-Financial",
            "type": "grafana-clickhouse-datasource",
            "url": "http://127.0.0.1:8123",
            "access": "proxy",
            "database": "financial_metrics",
            "user": "default",
            "basicAuth": False,
            "isDefault": True,
            "jsonData": {
                "defaultDatabase": "financial_metrics",
                "port": 8123,
                "server": "127.0.0.1",
                "username": "default",
                "protocol": "http",
                "secure": False,
                "tlsSkipVerify": True,
                "dialTimeout": 30,
                "queryTimeout": 300,
                "idleTimeout": 300,
                "maxOpenConns": 10,
                "maxIdleConns": 5,
                "connMaxLifetime": 3600
            },
            "secureJsonData": {}
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/datasources",
                json=datasource_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                logger.info("✓ 修复的ClickHouse数据源创建成功")
                return True
            else:
                logger.error(f"数据源创建失败: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"数据源创建失败: {e}")
            return False
    
    def test_datasource(self) -> bool:
        """测试数据源连接"""
        logger.info("测试数据源连接...")
        
        try:
            # 获取数据源
            response = self.session.get(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code != 200:
                logger.error("无法找到数据源")
                return False
            
            datasource = response.json()
            datasource_id = datasource['id']
            
            # 测试连接
            test_response = self.session.post(f"{self.base_url}/api/datasources/{datasource_id}/health")
            
            logger.info(f"连接测试响应: {test_response.status_code}")
            if test_response.status_code == 200:
                logger.info("✓ 数据源连接测试成功")
                return True
            else:
                logger.warning(f"连接测试失败: {test_response.text}")
                return False
                
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
    
    def test_query(self) -> bool:
        """测试查询"""
        logger.info("测试数据查询...")
        
        try:
            # 获取数据源
            response = self.session.get(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code != 200:
                logger.error("无法找到数据源")
                return False
            
            datasource = response.json()
            datasource_uid = datasource['uid']
            
            # 构造查询请求
            query_data = {
                "queries": [
                    {
                        "datasource": {
                            "type": "grafana-clickhouse-datasource",
                            "uid": datasource_uid
                        },
                        "rawSql": "SELECT count() as count FROM financial_metrics.trade_metrics_minutely",
                        "refId": "A"
                    }
                ],
                "from": "now-1h",
                "to": "now"
            }
            
            query_response = self.session.post(
                f"{self.base_url}/api/ds/query",
                json=query_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if query_response.status_code == 200:
                result = query_response.json()
                logger.info(f"✓ 查询测试成功: {result}")
                return True
            else:
                logger.error(f"查询测试失败: {query_response.status_code} - {query_response.text}")
                return False
                
        except Exception as e:
            logger.error(f"查询测试失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 修复Grafana数据源 ===\n")
    
    fixer = DataSourceFixer()
    
    # 1. 删除现有数据源
    if not fixer.delete_existing_datasource():
        return False
    
    # 2. 创建修复的数据源
    if not fixer.create_fixed_datasource():
        return False
    
    # 3. 测试连接
    if not fixer.test_datasource():
        logger.warning("连接测试失败，但继续执行...")
    
    # 4. 测试查询
    if not fixer.test_query():
        logger.warning("查询测试失败，但继续执行...")
    
    print("\n✓ 数据源修复完成！")
    print("请刷新Grafana仪表盘页面")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
