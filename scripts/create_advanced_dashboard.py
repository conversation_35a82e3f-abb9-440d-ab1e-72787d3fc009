#!/usr/bin/env python3
"""
创建高级金融分析仪表盘
包含技术指标和深度分析
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AdvancedDashboardCreator:
    """高级仪表盘创建器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def create_technical_analysis_dashboard(self) -> bool:
        """创建技术分析仪表盘"""
        logger.info("创建技术分析仪表盘...")
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": "BTCUSDT 技术分析仪表盘",
                "tags": ["technical-analysis", "btc", "trading"],
                "timezone": "Asia/Shanghai",
                "refresh": "1m",
                "time": {
                    "from": "now-6h",
                    "to": "now"
                },
                "panels": [
                    # 主K线图
                    {
                        "id": 1,
                        "title": "BTCUSDT K线图 + 移动平均线",
                        "type": "timeseries",
                        "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    close_price as "收盘价",
                                    avg(close_price) OVER (
                                        ORDER BY metric_time 
                                        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
                                    ) as "MA20",
                                    avg(close_price) OVER (
                                        ORDER BY metric_time 
                                        ROWS BETWEEN 49 PRECEDING AND CURRENT ROW
                                    ) as "MA50"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 2,
                                    "fillOpacity": 0,
                                    "gradientMode": "none",
                                    "showPoints": "never",
                                    "pointSize": 5
                                },
                                "unit": "currencyUSD"
                            },
                            "overrides": [
                                {
                                    "matcher": {"id": "byName", "options": "收盘价"},
                                    "properties": [
                                        {"id": "color", "value": {"mode": "fixed", "fixedColor": "blue"}},
                                        {"id": "custom.lineWidth", "value": 3}
                                    ]
                                },
                                {
                                    "matcher": {"id": "byName", "options": "MA20"},
                                    "properties": [
                                        {"id": "color", "value": {"mode": "fixed", "fixedColor": "orange"}},
                                        {"id": "custom.lineWidth", "value": 2}
                                    ]
                                },
                                {
                                    "matcher": {"id": "byName", "options": "MA50"},
                                    "properties": [
                                        {"id": "color", "value": {"mode": "fixed", "fixedColor": "red"}},
                                        {"id": "custom.lineWidth", "value": 2}
                                    ]
                                }
                            ]
                        }
                    },
                    # 成交量分析
                    {
                        "id": 2,
                        "title": "成交量分析",
                        "type": "timeseries",
                        "gridPos": {"h": 6, "w": 12, "x": 0, "y": 12},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    total_volume as "总成交量",
                                    buy_volume as "买入量",
                                    sell_volume as "卖出量",
                                    (buy_volume / total_volume) * 100 as "买入占比%"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "bars",
                                    "barAlignment": 0,
                                    "fillOpacity": 80
                                },
                                "unit": "short"
                            },
                            "overrides": [
                                {
                                    "matcher": {"id": "byName", "options": "买入占比%"},
                                    "properties": [
                                        {"id": "custom.axisPlacement", "value": "right"},
                                        {"id": "custom.drawStyle", "value": "line"},
                                        {"id": "unit", "value": "percent"},
                                        {"id": "color", "value": {"mode": "fixed", "fixedColor": "purple"}}
                                    ]
                                }
                            ]
                        }
                    },
                    # VWAP分析
                    {
                        "id": 3,
                        "title": "VWAP vs 收盘价",
                        "type": "timeseries",
                        "gridPos": {"h": 6, "w": 12, "x": 12, "y": 12},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    close_price as "收盘价",
                                    vwap as "VWAP",
                                    (close_price - vwap) as "价差",
                                    ((close_price - vwap) / vwap) * 100 as "偏离度%"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 2
                                },
                                "unit": "currencyUSD"
                            },
                            "overrides": [
                                {
                                    "matcher": {"id": "byName", "options": "偏离度%"},
                                    "properties": [
                                        {"id": "custom.axisPlacement", "value": "right"},
                                        {"id": "unit", "value": "percent"},
                                        {"id": "color", "value": {"mode": "fixed", "fixedColor": "red"}}
                                    ]
                                }
                            ]
                        }
                    },
                    # 价格波动率
                    {
                        "id": 4,
                        "title": "价格波动分析",
                        "type": "timeseries",
                        "gridPos": {"h": 6, "w": 12, "x": 0, "y": 18},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    metric_time as time,
                                    (high_price - low_price) as "价格区间",
                                    ((high_price - low_price) / close_price) * 100 as "波动率%",
                                    (close_price - open_price) as "涨跌额",
                                    ((close_price - open_price) / open_price) * 100 as "涨跌幅%"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                ORDER BY metric_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 1
                                },
                                "unit": "currencyUSD"
                            },
                            "overrides": [
                                {
                                    "matcher": {"id": "byRegexp", "options": ".*%"},
                                    "properties": [
                                        {"id": "unit", "value": "percent"},
                                        {"id": "custom.axisPlacement", "value": "right"}
                                    ]
                                }
                            ]
                        }
                    },
                    # 订单簿深度
                    {
                        "id": 5,
                        "title": "订单簿分析",
                        "type": "timeseries",
                        "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    snapshot_time as time,
                                    best_bid as "最优买价",
                                    best_ask as "最优卖价",
                                    spread as "价差",
                                    (spread / mid_price) * 10000 as "价差基点"
                                FROM financial_metrics.orderbook_snapshots 
                                WHERE symbol = 'BTCUSDT'
                                AND snapshot_time >= $__fromTime AND snapshot_time <= $__toTime
                                ORDER BY snapshot_time
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 1
                                },
                                "unit": "currencyUSD"
                            },
                            "overrides": [
                                {
                                    "matcher": {"id": "byName", "options": "价差基点"},
                                    "properties": [
                                        {"id": "unit", "value": "short"},
                                        {"id": "custom.axisPlacement", "value": "right"},
                                        {"id": "color", "value": {"mode": "fixed", "fixedColor": "purple"}}
                                    ]
                                }
                            ]
                        }
                    },
                    # 实时统计
                    {
                        "id": 6,
                        "title": "实时统计",
                        "type": "stat",
                        "gridPos": {"h": 4, "w": 24, "x": 0, "y": 24},
                        "targets": [
                            {
                                "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-financial"},
                                "rawSql": """
                                SELECT 
                                    max(close_price) as "最新价格",
                                    min(low_price) as "最低价",
                                    max(high_price) as "最高价",
                                    sum(total_volume) as "总成交量",
                                    sum(total_turnover) as "总成交额",
                                    avg(vwap) as "平均VWAP",
                                    sum(trade_count) as "交易笔数"
                                FROM financial_metrics.trade_metrics_minutely 
                                WHERE contract_code = 'BTCUSDT'
                                AND metric_time >= $__fromTime AND metric_time <= $__toTime
                                """,
                                "refId": "A"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "short"
                            },
                            "overrides": [
                                {
                                    "matcher": {"id": "byRegexp", "options": ".*价格|.*价|VWAP"},
                                    "properties": [
                                        {"id": "unit", "value": "currencyUSD"}
                                    ]
                                }
                            ]
                        },
                        "options": {
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"],
                                "fields": ""
                            },
                            "orientation": "auto",
                            "textMode": "auto",
                            "colorMode": "value",
                            "graphMode": "none",
                            "justifyMode": "auto"
                        }
                    }
                ]
            },
            "folderId": 1,
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                logger.info("✓ 技术分析仪表盘创建成功")
                logger.info(f"仪表盘URL: {self.base_url}/d/{result.get('uid', '')}")
                return True
            else:
                logger.error(f"仪表盘创建失败: {response.status_code} - {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"仪表盘创建失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 创建高级技术分析仪表盘 ===\n")
    
    creator = AdvancedDashboardCreator()
    
    # 创建技术分析仪表盘
    if creator.create_technical_analysis_dashboard():
        print("\n✓ 高级仪表盘创建完成！")
        print("访问地址: http://localhost:3000")
        return True
    else:
        print("\n✗ 高级仪表盘创建失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
