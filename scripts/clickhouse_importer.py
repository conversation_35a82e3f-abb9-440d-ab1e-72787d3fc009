#!/usr/bin/env python3
"""
ClickHouse数据导入模块
实现批量数据导入和验证功能
"""

import pandas as pd
from clickhouse_driver import Client
from typing import Dict, List, Tuple, Optional
import logging
from tqdm import tqdm
import time

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ClickHouseImporter:
    """ClickHouse数据导入器"""
    
    def __init__(self, host='localhost', port=9000, database='financial_metrics'):
        self.host = host
        self.port = port
        self.database = database
        self.client = None
        self.connect()
    
    def connect(self) -> None:
        """连接到ClickHouse"""
        try:
            self.client = Client(
                host=self.host,
                port=self.port,
                database=self.database
            )
            # 测试连接
            result = self.client.execute('SELECT 1')
            logger.info(f"成功连接到ClickHouse: {self.host}:{self.port}/{self.database}")
        except Exception as e:
            logger.error(f"ClickHouse连接失败: {e}")
            raise
    
    def execute_query(self, query: str) -> List:
        """执行查询"""
        try:
            return self.client.execute(query)
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise
    
    def insert_dataframe(self, df: pd.DataFrame, table_name: str, batch_size: int = 10000) -> bool:
        """批量插入DataFrame数据"""
        if df is None or len(df) == 0:
            logger.warning("数据为空，跳过插入")
            return True
        
        logger.info(f"开始插入数据到表 {table_name}: {len(df)} 条记录")
        
        try:
            # 获取表结构
            table_info = self.get_table_info(table_name)
            if not table_info:
                logger.error(f"无法获取表 {table_name} 的结构信息")
                return False
            
            # 验证数据格式
            if not self._validate_dataframe_format(df, table_name):
                return False
            
            # 分批插入
            total_batches = (len(df) + batch_size - 1) // batch_size
            
            with tqdm(total=len(df), desc=f"插入 {table_name}") as pbar:
                for i in range(0, len(df), batch_size):
                    batch_df = df.iloc[i:i + batch_size]
                    
                    # 转换为列表格式
                    data = batch_df.values.tolist()
                    
                    # 插入数据
                    self.client.execute(
                        f'INSERT INTO {table_name} VALUES',
                        data
                    )
                    
                    pbar.update(len(batch_df))
                    
                    # 避免过快插入
                    if i > 0 and i % (batch_size * 10) == 0:
                        time.sleep(0.1)
            
            logger.info(f"数据插入完成: {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"数据插入失败: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> Dict:
        """获取表结构信息"""
        try:
            # 解析表名
            parts = table_name.split('.')
            if len(parts) == 2:
                database, table = parts
            else:
                database = self.database
                table = table_name

            # 使用系统表获取详细信息
            query = f"""
            SELECT name, type, default_kind
            FROM system.columns
            WHERE database = '{database}' AND table = '{table}'
            ORDER BY position
            """
            result = self.client.execute(query)

            columns = {}
            for row in result:
                col_name, col_type, default_kind = row
                columns[col_name] = {
                    'type': col_type,
                    'default_kind': default_kind or ''
                }

            return columns
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            return {}
    
    def _validate_dataframe_format(self, df: pd.DataFrame, table_name: str) -> bool:
        """验证DataFrame格式是否匹配表结构"""
        table_info = self.get_table_info(table_name)
        
        if not table_info:
            return False
        
        # 检查列数量
        all_columns = list(table_info.keys())
        df_columns = list(df.columns)

        # 过滤掉MATERIALIZED、DEFAULT、ALIAS等自动生成的列
        materialized_columns = []
        expected_columns = []

        for col, col_info in table_info.items():
            default_kind = col_info.get('default_kind', '')
            if default_kind in ['MATERIALIZED', 'DEFAULT', 'ALIAS']:
                materialized_columns.append(col)
            else:
                expected_columns.append(col)

        logger.info(f"表 {table_name} 需要的列: {expected_columns}")
        logger.info(f"数据包含的列: {df_columns}")
        logger.info(f"过滤的列: {materialized_columns}")
        
        if len(df_columns) != len(expected_columns):
            logger.error(f"列数量不匹配: 期望 {len(expected_columns)}, 实际 {len(df_columns)}")
            logger.error(f"期望列: {expected_columns}")
            logger.error(f"实际列: {df_columns}")
            return False
        
        logger.info(f"数据格式验证通过: {table_name}")
        return True
    
    def get_table_count(self, table_name: str) -> int:
        """获取表记录数"""
        try:
            result = self.client.execute(f'SELECT count() FROM {table_name}')
            return result[0][0]
        except Exception as e:
            logger.error(f"获取表记录数失败: {e}")
            return 0
    
    def verify_import(self, table_name: str, expected_count: int) -> bool:
        """验证导入结果"""
        actual_count = self.get_table_count(table_name)
        
        if actual_count == expected_count:
            logger.info(f"✓ 表 {table_name} 导入验证成功: {actual_count} 条记录")
            return True
        else:
            logger.error(f"✗ 表 {table_name} 导入验证失败: 期望 {expected_count}, 实际 {actual_count}")
            return False
    
    def get_sample_data(self, table_name: str, limit: int = 5) -> List:
        """获取样本数据"""
        try:
            query = f'SELECT * FROM {table_name} LIMIT {limit}'
            return self.client.execute(query)
        except Exception as e:
            logger.error(f"获取样本数据失败: {e}")
            return []
    
    def optimize_table(self, table_name: str) -> bool:
        """优化表性能"""
        try:
            logger.info(f"优化表 {table_name}...")
            self.client.execute(f'OPTIMIZE TABLE {table_name}')
            logger.info(f"✓ 表 {table_name} 优化完成")
            return True
        except Exception as e:
            logger.error(f"表优化失败: {e}")
            return False
    
    def get_import_summary(self) -> Dict:
        """获取导入摘要"""
        tables = ['raw_trades', 'trade_metrics_minutely', 'orderbook_snapshots']
        summary = {}
        
        for table in tables:
            full_table_name = f"{self.database}.{table}"
            count = self.get_table_count(full_table_name)
            summary[table] = {
                'count': count,
                'sample': self.get_sample_data(full_table_name, 3) if count > 0 else []
            }
        
        return summary
    
    def clear_table(self, table_name: str) -> bool:
        """清空表数据"""
        try:
            logger.warning(f"清空表 {table_name} 的所有数据...")
            self.client.execute(f'TRUNCATE TABLE {table_name}')
            logger.info(f"✓ 表 {table_name} 已清空")
            return True
        except Exception as e:
            logger.error(f"清空表失败: {e}")
            return False
    
    def close(self) -> None:
        """关闭连接"""
        if self.client:
            self.client.disconnect()
            logger.info("ClickHouse连接已关闭")

def main():
    """测试函数"""
    importer = ClickHouseImporter()
    
    # 获取表信息
    tables = ['raw_trades', 'trade_metrics_minutely', 'orderbook_snapshots']
    
    for table in tables:
        full_table_name = f"financial_metrics.{table}"
        info = importer.get_table_info(full_table_name)
        count = importer.get_table_count(full_table_name)
        print(f"\n表 {table}:")
        print(f"  记录数: {count}")
        print(f"  字段数: {len(info)}")
        if info:
            print(f"  字段: {list(info.keys())}")
    
    # 获取导入摘要
    summary = importer.get_import_summary()
    print(f"\n导入摘要: {summary}")
    
    importer.close()

if __name__ == "__main__":
    main()
