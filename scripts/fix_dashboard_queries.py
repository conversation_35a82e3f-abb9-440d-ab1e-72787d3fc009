#!/usr/bin/env python3
"""
修复仪表盘查询语法
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DashboardQueryFixer:
    """仪表盘查询修复器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def get_datasource_uid(self) -> str:
        """获取数据源UID"""
        try:
            response = self.session.get(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code == 200:
                datasource = response.json()
                return datasource['uid']
            else:
                logger.error(f"无法获取数据源: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"获取数据源失败: {e}")
            return None
    
    def create_working_dashboard(self) -> bool:
        """创建可工作的仪表盘"""
        logger.info("创建修复后的金融指标仪表盘...")
        
        datasource_uid = self.get_datasource_uid()
        if not datasource_uid:
            logger.error("无法获取数据源UID")
            return False
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": "金融交易指标仪表盘 (修复版)",
                "tags": ["financial", "trading", "btc", "fixed"],
                "timezone": "Asia/Shanghai",
                "refresh": "30s",
                "time": {
                    "from": "now-6h",
                    "to": "now"
                },
                "panels": [
                    # 价格走势图
                    {
                        "id": 1,
                        "title": "BTCUSDT 价格走势",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT metric_time, close_price FROM financial_metrics.trade_metrics_minutely WHERE contract_code = 'BTCUSDT' ORDER BY metric_time",
                                "refId": "A",
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 2,
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "showPoints": "never",
                                    "pointSize": 5
                                },
                                "unit": "currencyUSD"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "single", "sort": "none"}
                        }
                    },
                    # 成交量图
                    {
                        "id": 2,
                        "title": "成交量",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT metric_time, total_volume, buy_volume, sell_volume FROM financial_metrics.trade_metrics_minutely WHERE contract_code = 'BTCUSDT' ORDER BY metric_time",
                                "refId": "A",
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 1,
                                    "fillOpacity": 10,
                                    "gradientMode": "none",
                                    "showPoints": "never",
                                    "pointSize": 5
                                },
                                "unit": "short"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "single", "sort": "none"}
                        }
                    },
                    # OHLC数据
                    {
                        "id": 3,
                        "title": "OHLC价格数据",
                        "type": "timeseries",
                        "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT metric_time, open_price, high_price, low_price, close_price FROM financial_metrics.trade_metrics_minutely WHERE contract_code = 'BTCUSDT' ORDER BY metric_time",
                                "refId": "A",
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 1,
                                    "fillOpacity": 0,
                                    "gradientMode": "none",
                                    "showPoints": "never",
                                    "pointSize": 5
                                },
                                "unit": "currencyUSD"
                            }
                        }
                    },
                    # 统计数据
                    {
                        "id": 4,
                        "title": "交易统计",
                        "type": "stat",
                        "gridPos": {"h": 6, "w": 12, "x": 0, "y": 18},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT avg(close_price) as avg_price, sum(total_volume) as total_vol, sum(total_turnover) as total_turnover, sum(trade_count) as total_trades FROM financial_metrics.trade_metrics_minutely WHERE contract_code = 'BTCUSDT'",
                                "refId": "A",
                                "format": "table"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "short"
                            }
                        },
                        "options": {
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"],
                                "fields": ""
                            },
                            "orientation": "auto",
                            "textMode": "auto",
                            "colorMode": "value",
                            "graphMode": "none",
                            "justifyMode": "auto"
                        }
                    },
                    # VWAP对比
                    {
                        "id": 5,
                        "title": "价格 vs VWAP",
                        "type": "timeseries",
                        "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT metric_time, close_price, vwap FROM financial_metrics.trade_metrics_minutely WHERE contract_code = 'BTCUSDT' ORDER BY metric_time",
                                "refId": "A",
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 2,
                                    "fillOpacity": 0,
                                    "gradientMode": "none",
                                    "showPoints": "never",
                                    "pointSize": 5
                                },
                                "unit": "currencyUSD"
                            }
                        },
                        "options": {
                            "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"},
                            "tooltip": {"mode": "single", "sort": "none"}
                        }
                    }
                ]
            },
            "folderId": 0,
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                logger.info("✓ 修复版仪表盘创建成功")
                logger.info(f"仪表盘URL: {self.base_url}/d/{result.get('uid', '')}")
                return True
            else:
                logger.error(f"仪表盘创建失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"仪表盘创建失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 修复仪表盘查询 ===\n")
    
    fixer = DashboardQueryFixer()
    
    if fixer.create_working_dashboard():
        print("\n✓ 修复版仪表盘创建完成！")
        print("请访问Grafana查看修复结果")
        return True
    else:
        print("\n✗ 修复版仪表盘创建失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
