#!/usr/bin/env python3
"""
订单簿数据处理器
抽取每分钟的订单簿快照数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Tuple, Dict
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OrderbookProcessor:
    """订单簿数据处理器"""
    
    def __init__(self, data_dir: str = "tardis_shell_data"):
        self.data_dir = Path(data_dir)
        
    def load_orderbook_snapshot(self, filename: str) -> pd.DataFrame:
        """加载订单簿快照数据"""
        file_path = self.data_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        logger.info(f"加载订单簿快照数据: {filename}")
        
        # 读取CSV数据
        df = pd.read_csv(file_path)
        
        # 数据类型转换
        df['timestamp'] = pd.to_numeric(df['timestamp'])
        df['local_timestamp'] = pd.to_numeric(df['local_timestamp'])
        
        # 创建datetime列
        df['snapshot_time'] = pd.to_datetime(df['timestamp'], unit='us')
        
        logger.info(f"成功加载 {len(df)} 条订单簿快照记录")
        return df
    
    def sample_minutely_snapshots(self, df: pd.DataFrame) -> pd.DataFrame:
        """抽取每分钟的订单簿快照"""
        logger.info("开始抽取每分钟订单簿快照...")
        
        # 创建分钟级时间戳
        df['minute_time'] = df['snapshot_time'].dt.floor('min')
        
        # 按分钟分组，取每分钟的第一条记录
        sampled_df = df.groupby(['symbol', 'minute_time']).first().reset_index()
        
        logger.info(f"抽样完成: 从 {len(df)} 条记录中抽取了 {len(sampled_df)} 条分钟级快照")
        
        return sampled_df
    
    def parse_orderbook_levels(self, row: pd.Series, max_levels: int = 25) -> Tuple[List[Tuple], List[Tuple]]:
        """解析订单簿档位数据"""
        asks = []
        bids = []
        
        for i in range(max_levels):
            # 解析asks
            ask_price_col = f'asks[{i}].price'
            ask_amount_col = f'asks[{i}].amount'
            
            if ask_price_col in row and not pd.isna(row[ask_price_col]):
                price = float(row[ask_price_col])
                amount = float(row[ask_amount_col])
                if price > 0 and amount > 0:
                    asks.append((price, amount))
            
            # 解析bids
            bid_price_col = f'bids[{i}].price'
            bid_amount_col = f'bids[{i}].amount'
            
            if bid_price_col in row and not pd.isna(row[bid_price_col]):
                price = float(row[bid_price_col])
                amount = float(row[bid_amount_col])
                if price > 0 and amount > 0:
                    bids.append((price, amount))
        
        return asks, bids
    
    def process_orderbook_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理订单簿数据为ClickHouse格式"""
        logger.info("处理订单簿数据...")
        
        processed_data = []
        
        for _, row in df.iterrows():
            # 解析asks和bids
            asks, bids = self.parse_orderbook_levels(row)
            
            # 计算关键指标
            best_ask = asks[0][0] if asks else None
            best_bid = bids[0][0] if bids else None
            spread = best_ask - best_bid if best_ask and best_bid else None
            mid_price = (best_ask + best_bid) / 2 if best_ask and best_bid else None
            
            processed_data.append({
                'exchange': row['exchange'],
                'symbol': row['symbol'],
                'timestamp': int(row['timestamp']),
                'local_timestamp': int(row['local_timestamp']),
                'asks': asks,
                'bids': bids,
                'best_ask': best_ask,
                'best_bid': best_bid,
                'spread': spread,
                'mid_price': mid_price
            })
        
        result_df = pd.DataFrame(processed_data)
        logger.info(f"处理完成: {len(result_df)} 条订单簿记录")
        
        return result_df
    
    def get_orderbook_summary(self, df: pd.DataFrame) -> Dict:
        """获取订单簿数据摘要"""
        if df is None or len(df) == 0:
            return {}
        
        summary = {
            'total_snapshots': len(df),
            'symbols': df['symbol'].unique().tolist() if 'symbol' in df.columns else [],
            'time_range': {
                'start': df['snapshot_time'].min() if 'snapshot_time' in df.columns else None,
                'end': df['snapshot_time'].max() if 'snapshot_time' in df.columns else None
            }
        }
        
        if 'best_ask' in df.columns and 'best_bid' in df.columns:
            summary.update({
                'price_range': {
                    'best_ask_min': df['best_ask'].min(),
                    'best_ask_max': df['best_ask'].max(),
                    'best_bid_min': df['best_bid'].min(),
                    'best_bid_max': df['best_bid'].max()
                },
                'spread_stats': {
                    'min_spread': df['spread'].min(),
                    'max_spread': df['spread'].max(),
                    'avg_spread': df['spread'].mean()
                }
            })
        
        return summary

def main():
    """测试函数"""
    processor = OrderbookProcessor()
    
    # 加载订单簿数据
    filename = "binance-futures_book_snapshot_25_20250701_BTCUSDT.csv"
    
    try:
        # 1. 加载原始数据
        logger.info("=== 加载订单簿数据 ===")
        df = processor.load_orderbook_snapshot(filename)
        
        # 2. 抽取每分钟快照
        logger.info("=== 抽取分钟级快照 ===")
        sampled_df = processor.sample_minutely_snapshots(df)
        
        # 3. 处理数据
        logger.info("=== 处理订单簿数据 ===")
        processed_df = processor.process_orderbook_data(sampled_df)
        
        # 4. 数据摘要
        summary = processor.get_orderbook_summary(processed_df)
        print("\n=== 订单簿数据摘要 ===")
        print(f"总快照数: {summary.get('total_snapshots', 0)}")
        print(f"交易对: {summary.get('symbols', [])}")
        print(f"时间范围: {summary.get('time_range', {})}")
        
        if 'spread_stats' in summary:
            spread_stats = summary['spread_stats']
            print(f"价差统计:")
            print(f"  最小价差: {spread_stats['min_spread']:.4f}")
            print(f"  最大价差: {spread_stats['max_spread']:.4f}")
            print(f"  平均价差: {spread_stats['avg_spread']:.4f}")
        
        # 5. 显示样本数据
        print("\n=== 样本数据 ===")
        if len(processed_df) > 0:
            sample = processed_df.iloc[0]
            print(f"交易所: {sample['exchange']}")
            print(f"交易对: {sample['symbol']}")
            print(f"最优买价: {sample['best_bid']}")
            print(f"最优卖价: {sample['best_ask']}")
            print(f"价差: {sample['spread']}")
            print(f"中间价: {sample['mid_price']}")
            print(f"买盘档数: {len(sample['bids'])}")
            print(f"卖盘档数: {len(sample['asks'])}")
        
        return processed_df
        
    except FileNotFoundError as e:
        logger.error(f"文件未找到: {e}")
        return None
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        return None

if __name__ == "__main__":
    main()
