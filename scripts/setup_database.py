#!/usr/bin/env python3
"""
ClickHouse数据库初始化脚本
创建金融指标系统所需的数据库和表结构
"""

import requests
import sys
from typing import List, Tuple

class ClickHouseManager:
    def __init__(self, host="localhost", port=8123):
        self.base_url = f"http://{host}:{port}"
        
    def execute_query(self, query: str) -> Tuple[bool, str]:
        """执行ClickHouse查询"""
        try:
            response = requests.post(
                self.base_url,
                data=query,
                timeout=30
            )
            if response.status_code == 200:
                return True, response.text.strip()
            else:
                return False, f"查询失败 (HTTP {response.status_code}): {response.text}"
        except requests.exceptions.RequestException as e:
            return False, f"连接失败: {str(e)}"
    
    def create_database(self) -> bool:
        """创建金融指标数据库"""
        print("创建数据库 'financial_metrics'...")
        
        query = """
        CREATE DATABASE IF NOT EXISTS financial_metrics
        COMMENT '金融指标分析系统数据库'
        """
        
        success, result = self.execute_query(query)
        if success:
            print("✓ 数据库创建成功")
            return True
        else:
            print(f"✗ 数据库创建失败: {result}")
            return False
    
    def create_raw_trades_table(self) -> bool:
        """创建原始交易数据表"""
        print("创建原始交易数据表...")
        
        query = """
        CREATE TABLE IF NOT EXISTS financial_metrics.raw_trades (
            exchange String COMMENT '交易所名称',
            symbol String COMMENT '交易对符号',
            timestamp UInt64 COMMENT '交易时间戳(微秒)',
            local_timestamp UInt64 COMMENT '本地时间戳(微秒)',
            trade_id UInt64 COMMENT '交易ID',
            side Enum8('buy' = 1, 'sell' = 2) COMMENT '买卖方向',
            price Float64 COMMENT '交易价格',
            amount Float64 COMMENT '交易数量',
            trade_time DateTime64(6) MATERIALIZED fromUnixTimestamp64Micro(timestamp) COMMENT '交易时间'
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(trade_time)
        ORDER BY (symbol, trade_time, trade_id)
        SETTINGS index_granularity = 8192
        COMMENT '原始交易数据表'
        """
        
        success, result = self.execute_query(query)
        if success:
            print("✓ 原始交易数据表创建成功")
            return True
        else:
            print(f"✗ 原始交易数据表创建失败: {result}")
            return False
    
    def create_trade_metrics_table(self) -> bool:
        """创建交易指标表"""
        print("创建交易指标表...")
        
        query = """
        CREATE TABLE IF NOT EXISTS financial_metrics.trade_metrics_minutely (
            metric_time DateTime COMMENT '指标计算时间(精确到分钟)',
            contract_code String COMMENT '合约代码',
            open_price Float64 COMMENT '开盘价',
            close_price Float64 COMMENT '收盘价',
            high_price Float64 COMMENT '最高价',
            low_price Float64 COMMENT '最低价',
            total_volume Float64 COMMENT '总成交量',
            total_turnover Float64 COMMENT '总成交额',
            vwap Float64 COMMENT '成交量加权平均价',
            trade_count UInt32 COMMENT '交易笔数',
            buy_volume Float64 COMMENT '买入成交量',
            sell_volume Float64 COMMENT '卖出成交量',
            buy_turnover Float64 COMMENT '买入成交额',
            sell_turnover Float64 COMMENT '卖出成交额'
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(metric_time)
        ORDER BY (contract_code, metric_time)
        SETTINGS index_granularity = 8192
        COMMENT '分钟级交易指标表'
        """
        
        success, result = self.execute_query(query)
        if success:
            print("✓ 交易指标表创建成功")
            return True
        else:
            print(f"✗ 交易指标表创建失败: {result}")
            return False
    
    def create_orderbook_snapshot_table(self) -> bool:
        """创建订单簿快照表"""
        print("创建订单簿快照表...")
        
        query = """
        CREATE TABLE IF NOT EXISTS financial_metrics.orderbook_snapshots (
            exchange String COMMENT '交易所名称',
            symbol String COMMENT '交易对符号',
            timestamp UInt64 COMMENT '快照时间戳(微秒)',
            local_timestamp UInt64 COMMENT '本地时间戳(微秒)',
            snapshot_time DateTime64(6) MATERIALIZED fromUnixTimestamp64Micro(timestamp) COMMENT '快照时间',
            asks Array(Tuple(Float64, Float64)) COMMENT '卖盘数据[(价格,数量)]',
            bids Array(Tuple(Float64, Float64)) COMMENT '买盘数据[(价格,数量)]',
            best_ask Float64 COMMENT '最优卖价',
            best_bid Float64 COMMENT '最优买价',
            spread Float64 COMMENT '买卖价差',
            mid_price Float64 COMMENT '中间价'
        ) ENGINE = MergeTree()
        PARTITION BY toYYYYMM(snapshot_time)
        ORDER BY (symbol, snapshot_time)
        SETTINGS index_granularity = 8192
        COMMENT '订单簿快照表'
        """
        
        success, result = self.execute_query(query)
        if success:
            print("✓ 订单簿快照表创建成功")
            return True
        else:
            print(f"✗ 订单簿快照表创建失败: {result}")
            return False
    
    def create_views(self) -> bool:
        """创建常用视图"""
        print("创建常用视图...")

        # 暂时跳过视图创建，专注于基础表结构
        # 视图可以在后续通过Grafana查询时动态创建
        print("✓ 视图创建已跳过（将在Grafana中使用动态查询）")
        return True
    
    def verify_tables(self) -> bool:
        """验证表结构"""
        print("验证表结构...")
        
        tables_query = "SHOW TABLES FROM financial_metrics"
        success, result = self.execute_query(tables_query)
        
        if success:
            tables = result.split('\n') if result else []
            expected_tables = [
                'raw_trades',
                'trade_metrics_minutely', 
                'orderbook_snapshots'
            ]
            
            print(f"已创建的表: {', '.join(tables)}")
            
            missing_tables = [t for t in expected_tables if t not in tables]
            if missing_tables:
                print(f"✗ 缺少表: {', '.join(missing_tables)}")
                return False
            else:
                print("✓ 所有表结构验证通过")
                return True
        else:
            print(f"✗ 表验证失败: {result}")
            return False

def main():
    """主函数"""
    print("=== ClickHouse数据库初始化 ===\n")
    
    ch = ClickHouseManager()
    
    # 测试连接
    print("测试ClickHouse连接...")
    success, result = ch.execute_query("SELECT 1")
    if not success:
        print(f"✗ ClickHouse连接失败: {result}")
        sys.exit(1)
    print("✓ ClickHouse连接正常\n")
    
    # 执行初始化步骤
    steps = [
        ch.create_database,
        ch.create_raw_trades_table,
        ch.create_trade_metrics_table,
        ch.create_orderbook_snapshot_table,
        ch.create_views,
        ch.verify_tables
    ]
    
    for step in steps:
        if not step():
            print("\n✗ 数据库初始化失败")
            sys.exit(1)
        print()
    
    print("✓ 数据库初始化完成！")
    print("\n下一步: 运行 python scripts/import_data.py 导入交易数据")

if __name__ == "__main__":
    main()
