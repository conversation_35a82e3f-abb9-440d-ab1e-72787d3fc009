#!/usr/bin/env python3
"""
调试导入问题
"""

from data_processor import BinanceDataProcessor
from clickhouse_importer import ClickHouseImporter

def main():
    # 1. 处理数据
    processor = BinanceDataProcessor()
    trades_df = processor.load_trades_data("binance-futures_trades_20250701_BTCUSDT.csv")
    clean_df = processor.clean_trades_data(trades_df)
    raw_data = processor.prepare_for_clickhouse(clean_df, 'raw_trades')
    
    print("原始数据列:", list(raw_data.columns))
    print("原始数据形状:", raw_data.shape)
    print("原始数据样本:")
    print(raw_data.head(2))
    
    # 2. 检查表结构
    importer = ClickHouseImporter()
    table_info = importer.get_table_info('financial_metrics.raw_trades')
    print("\n表结构:")
    for col, col_type in table_info.items():
        print(f"  {col}: {col_type}")
    
    # 3. 尝试导入小样本
    sample_data = raw_data.head(100)
    print(f"\n尝试导入 {len(sample_data)} 条样本数据...")
    
    success = importer.insert_dataframe(sample_data, 'financial_metrics.raw_trades', batch_size=100)
    print(f"导入结果: {success}")
    
    if success:
        count = importer.get_table_count('financial_metrics.raw_trades')
        print(f"表中记录数: {count}")
    
    importer.close()

if __name__ == "__main__":
    main()
