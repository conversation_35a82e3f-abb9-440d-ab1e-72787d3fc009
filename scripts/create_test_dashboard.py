#!/usr/bin/env python3
"""
创建简单的测试仪表盘
"""

import requests
import json
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestDashboardCreator:
    """测试仪表盘创建器"""
    
    def __init__(self, host='localhost', port=3000, username='admin', password='admin123'):
        self.base_url = f"http://{host}:{port}"
        self.auth = (username, password)
        self.session = requests.Session()
        self.session.auth = self.auth
    
    def get_datasource_uid(self) -> str:
        """获取数据源UID"""
        try:
            response = self.session.get(f"{self.base_url}/api/datasources/name/ClickHouse-Financial")
            if response.status_code == 200:
                datasource = response.json()
                return datasource['uid']
            else:
                logger.error(f"无法获取数据源: {response.status_code}")
                return None
        except Exception as e:
            logger.error(f"获取数据源失败: {e}")
            return None
    
    def create_simple_test_dashboard(self) -> bool:
        """创建简单测试仪表盘"""
        logger.info("创建简单测试仪表盘...")
        
        datasource_uid = self.get_datasource_uid()
        if not datasource_uid:
            logger.error("无法获取数据源UID")
            return False
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": "ClickHouse连接测试",
                "tags": ["test"],
                "timezone": "Asia/Shanghai",
                "refresh": "30s",
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "panels": [
                    {
                        "id": 1,
                        "title": "数据记录数测试",
                        "type": "stat",
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT count() as record_count FROM financial_metrics.trade_metrics_minutely",
                                "refId": "A",
                                "format": "table"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "thresholds"},
                                "mappings": [],
                                "thresholds": {
                                    "mode": "absolute",
                                    "steps": [
                                        {"color": "green", "value": None}
                                    ]
                                },
                                "unit": "short"
                            }
                        },
                        "options": {
                            "reduceOptions": {
                                "values": False,
                                "calcs": ["lastNotNull"],
                                "fields": ""
                            },
                            "orientation": "auto",
                            "textMode": "auto",
                            "colorMode": "value",
                            "graphMode": "none",
                            "justifyMode": "auto"
                        }
                    },
                    {
                        "id": 2,
                        "title": "简单价格查询",
                        "type": "timeseries",
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                        "targets": [
                            {
                                "datasource": {
                                    "type": "grafana-clickhouse-datasource",
                                    "uid": datasource_uid
                                },
                                "rawSql": "SELECT metric_time, close_price FROM financial_metrics.trade_metrics_minutely ORDER BY metric_time LIMIT 100",
                                "refId": "A",
                                "format": "time_series"
                            }
                        ],
                        "fieldConfig": {
                            "defaults": {
                                "color": {"mode": "palette-classic"},
                                "custom": {
                                    "drawStyle": "line",
                                    "lineWidth": 2,
                                    "fillOpacity": 0,
                                    "gradientMode": "none",
                                    "showPoints": "never",
                                    "pointSize": 5
                                },
                                "unit": "currencyUSD"
                            }
                        }
                    }
                ]
            },
            "folderId": 0,
            "overwrite": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/dashboards/db",
                json=dashboard_config,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code in [200, 201]:
                result = response.json()
                logger.info("✓ 测试仪表盘创建成功")
                logger.info(f"仪表盘URL: {self.base_url}/d/{result.get('uid', '')}")
                return True
            else:
                logger.error(f"测试仪表盘创建失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"测试仪表盘创建失败: {e}")
            return False

def main():
    """主函数"""
    print("=== 创建测试仪表盘 ===\n")
    
    creator = TestDashboardCreator()
    
    if creator.create_simple_test_dashboard():
        print("\n✓ 测试仪表盘创建完成！")
        print("请访问Grafana查看测试结果")
        return True
    else:
        print("\n✗ 测试仪表盘创建失败")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
