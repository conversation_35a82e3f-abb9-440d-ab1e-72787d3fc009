#!/usr/bin/env python3
"""
数据验证脚本
验证导入数据的质量和完整性
"""

from clickhouse_driver import Client
import pandas as pd
from datetime import datetime, timedelta
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataValidator:
    """数据验证器"""
    
    def __init__(self, host='localhost', port=9000, database='financial_metrics'):
        self.client = Client(host=host, port=port, database=database)
        
    def validate_trade_metrics(self) -> bool:
        """验证交易指标数据"""
        logger.info("验证交易指标数据...")
        
        # 基础统计
        count_query = "SELECT count() FROM trade_metrics_minutely"
        total_count = self.client.execute(count_query)[0][0]
        logger.info(f"总记录数: {total_count}")
        
        if total_count == 0:
            logger.error("无交易指标数据")
            return False
        
        # 时间范围检查
        time_range_query = """
        SELECT 
            min(metric_time) as start_time,
            max(metric_time) as end_time,
            count(DISTINCT contract_code) as symbols
        FROM trade_metrics_minutely
        """
        
        result = self.client.execute(time_range_query)[0]
        start_time, end_time, symbols = result
        
        logger.info(f"时间范围: {start_time} 到 {end_time}")
        logger.info(f"交易对数量: {symbols}")
        
        # 数据质量检查
        quality_query = """
        SELECT 
            count() as total,
            countIf(open_price <= 0) as invalid_open,
            countIf(close_price <= 0) as invalid_close,
            countIf(high_price <= 0) as invalid_high,
            countIf(low_price <= 0) as invalid_low,
            countIf(total_volume <= 0) as invalid_volume,
            countIf(high_price < low_price) as invalid_range,
            countIf(vwap <= 0) as invalid_vwap
        FROM trade_metrics_minutely
        """
        
        quality_result = self.client.execute(quality_query)[0]
        total, invalid_open, invalid_close, invalid_high, invalid_low, invalid_volume, invalid_range, invalid_vwap = quality_result
        
        logger.info("数据质量检查:")
        logger.info(f"  无效开盘价: {invalid_open}/{total}")
        logger.info(f"  无效收盘价: {invalid_close}/{total}")
        logger.info(f"  无效最高价: {invalid_high}/{total}")
        logger.info(f"  无效最低价: {invalid_low}/{total}")
        logger.info(f"  无效成交量: {invalid_volume}/{total}")
        logger.info(f"  无效价格区间: {invalid_range}/{total}")
        logger.info(f"  无效VWAP: {invalid_vwap}/{total}")
        
        # 连续性检查
        continuity_query = """
        SELECT 
            contract_code,
            count() as minutes,
            min(metric_time) as start_time,
            max(metric_time) as end_time
        FROM trade_metrics_minutely
        GROUP BY contract_code
        """
        
        continuity_results = self.client.execute(continuity_query)
        
        for contract_code, minutes, start_time, end_time in continuity_results:
            expected_minutes = int((end_time - start_time).total_seconds() / 60) + 1
            logger.info(f"  {contract_code}: {minutes}/{expected_minutes} 分钟 ({minutes/expected_minutes*100:.1f}%)")
        
        # 统计摘要
        stats_query = """
        SELECT 
            contract_code,
            count() as records,
            avg(close_price) as avg_price,
            min(close_price) as min_price,
            max(close_price) as max_price,
            sum(total_volume) as total_volume,
            sum(total_turnover) as total_turnover
        FROM trade_metrics_minutely
        GROUP BY contract_code
        """
        
        stats_results = self.client.execute(stats_query)
        
        logger.info("统计摘要:")
        for contract_code, records, avg_price, min_price, max_price, total_volume, total_turnover in stats_results:
            logger.info(f"  {contract_code}:")
            logger.info(f"    记录数: {records}")
            logger.info(f"    平均价格: {avg_price:.2f}")
            logger.info(f"    价格区间: {min_price:.2f} - {max_price:.2f}")
            logger.info(f"    总成交量: {total_volume:.2f}")
            logger.info(f"    总成交额: {total_turnover:.2f}")
        
        # 判断验证结果
        issues = invalid_open + invalid_close + invalid_high + invalid_low + invalid_volume + invalid_range + invalid_vwap
        
        if issues == 0:
            logger.info("✓ 交易指标数据验证通过")
            return True
        else:
            logger.warning(f"✗ 发现 {issues} 个数据质量问题")
            return False
    
    def validate_raw_trades(self) -> bool:
        """验证原始交易数据"""
        logger.info("验证原始交易数据...")
        
        count_query = "SELECT count() FROM raw_trades"
        total_count = self.client.execute(count_query)[0][0]
        
        if total_count == 0:
            logger.warning("无原始交易数据")
            return True  # 原始数据是可选的
        
        logger.info(f"原始交易记录数: {total_count}")
        
        # 数据质量检查
        quality_query = """
        SELECT 
            count() as total,
            countIf(price <= 0) as invalid_price,
            countIf(amount <= 0) as invalid_amount,
            countIf(trade_id = 0) as invalid_id
        FROM raw_trades
        """
        
        quality_result = self.client.execute(quality_query)[0]
        total, invalid_price, invalid_amount, invalid_id = quality_result
        
        logger.info("原始数据质量检查:")
        logger.info(f"  无效价格: {invalid_price}/{total}")
        logger.info(f"  无效数量: {invalid_amount}/{total}")
        logger.info(f"  无效ID: {invalid_id}/{total}")
        
        issues = invalid_price + invalid_amount + invalid_id
        
        if issues == 0:
            logger.info("✓ 原始交易数据验证通过")
            return True
        else:
            logger.warning(f"✗ 发现 {issues} 个原始数据质量问题")
            return False
    
    def validate_data_consistency(self) -> bool:
        """验证数据一致性"""
        logger.info("验证数据一致性...")
        
        # 检查分钟级数据的时间连续性
        gap_query = """
        WITH time_series AS (
            SELECT 
                contract_code,
                metric_time,
                lagInFrame(metric_time) OVER (PARTITION BY contract_code ORDER BY metric_time) as prev_time
            FROM trade_metrics_minutely
        )
        SELECT 
            contract_code,
            count() as gaps
        FROM time_series
        WHERE dateDiff('minute', prev_time, metric_time) > 1
        GROUP BY contract_code
        """
        
        gap_results = self.client.execute(gap_query)
        
        if gap_results:
            logger.warning("发现时间间隙:")
            for contract_code, gaps in gap_results:
                logger.warning(f"  {contract_code}: {gaps} 个间隙")
            return False
        else:
            logger.info("✓ 时间连续性验证通过")
            return True
    
    def generate_report(self) -> dict:
        """生成验证报告"""
        logger.info("生成数据验证报告...")
        
        report = {
            'timestamp': datetime.now(),
            'tables': {},
            'overall_status': 'PASS'
        }
        
        # 检查各表
        tables = ['raw_trades', 'trade_metrics_minutely', 'orderbook_snapshots']
        
        for table in tables:
            count_query = f"SELECT count() FROM {table}"
            try:
                count = self.client.execute(count_query)[0][0]
                report['tables'][table] = {
                    'count': count,
                    'status': 'OK' if count > 0 else 'EMPTY'
                }
            except Exception as e:
                report['tables'][table] = {
                    'count': 0,
                    'status': 'ERROR',
                    'error': str(e)
                }
                report['overall_status'] = 'FAIL'
        
        return report

def main():
    """主函数"""
    validator = DataValidator()
    
    print("=== 数据验证报告 ===\n")
    
    # 执行验证
    metrics_ok = validator.validate_trade_metrics()
    trades_ok = validator.validate_raw_trades()
    consistency_ok = validator.validate_data_consistency()
    
    # 生成报告
    report = validator.generate_report()
    
    print(f"\n=== 验证摘要 ===")
    print(f"验证时间: {report['timestamp']}")
    print(f"整体状态: {report['overall_status']}")
    
    for table, info in report['tables'].items():
        print(f"{table}: {info['count']} 条记录 ({info['status']})")
    
    # 总结
    all_ok = metrics_ok and trades_ok and consistency_ok
    
    if all_ok:
        print("\n✓ 所有验证通过，数据质量良好")
    else:
        print("\n✗ 部分验证失败，请检查数据质量")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
