#!/usr/bin/env python3
"""
数据摘要报告生成器
生成完整的数据导入和质量报告
"""

from clickhouse_driver import Client
import pandas as pd
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataSummaryGenerator:
    """数据摘要报告生成器"""
    
    def __init__(self, host='localhost', port=9000, database='financial_metrics'):
        self.client = Client(host=host, port=port, database=database)
        
    def get_table_summary(self) -> dict:
        """获取所有表的基本信息"""
        tables = ['raw_trades', 'trade_metrics_minutely', 'orderbook_snapshots']
        summary = {}
        
        for table in tables:
            try:
                # 获取记录数
                count_query = f"SELECT count() FROM {table}"
                count = self.client.execute(count_query)[0][0]
                
                # 获取时间范围
                if table == 'raw_trades':
                    time_query = f"SELECT min(trade_time), max(trade_time) FROM {table}"
                elif table == 'trade_metrics_minutely':
                    time_query = f"SELECT min(metric_time), max(metric_time) FROM {table}"
                elif table == 'orderbook_snapshots':
                    time_query = f"SELECT min(snapshot_time), max(snapshot_time) FROM {table}"
                
                if count > 0:
                    time_result = self.client.execute(time_query)[0]
                    min_time, max_time = time_result
                else:
                    min_time, max_time = None, None
                
                summary[table] = {
                    'count': count,
                    'min_time': min_time,
                    'max_time': max_time
                }
                
            except Exception as e:
                logger.error(f"获取表 {table} 信息失败: {e}")
                summary[table] = {'count': 0, 'min_time': None, 'max_time': None}
        
        return summary
    
    def get_trading_metrics(self) -> dict:
        """获取交易指标统计"""
        try:
            query = """
            SELECT 
                contract_code,
                count() as minutes,
                min(close_price) as min_price,
                max(close_price) as max_price,
                avg(close_price) as avg_price,
                sum(total_volume) as total_volume,
                sum(total_turnover) as total_turnover,
                avg(vwap) as avg_vwap,
                sum(trade_count) as total_trades
            FROM trade_metrics_minutely
            GROUP BY contract_code
            """
            
            results = self.client.execute(query)
            
            metrics = {}
            for row in results:
                contract_code, minutes, min_price, max_price, avg_price, total_volume, total_turnover, avg_vwap, total_trades = row
                metrics[contract_code] = {
                    'minutes': minutes,
                    'price_range': {'min': min_price, 'max': max_price, 'avg': avg_price},
                    'volume': total_volume,
                    'turnover': total_turnover,
                    'avg_vwap': avg_vwap,
                    'total_trades': total_trades
                }
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取交易指标失败: {e}")
            return {}
    
    def get_orderbook_metrics(self) -> dict:
        """获取订单簿指标统计"""
        try:
            query = """
            SELECT 
                symbol,
                count() as snapshots,
                min(best_bid) as min_bid,
                max(best_ask) as max_ask,
                avg(spread) as avg_spread,
                min(spread) as min_spread,
                max(spread) as max_spread
            FROM orderbook_snapshots
            GROUP BY symbol
            """
            
            results = self.client.execute(query)
            
            metrics = {}
            for row in results:
                symbol, snapshots, min_bid, max_ask, avg_spread, min_spread, max_spread = row
                metrics[symbol] = {
                    'snapshots': snapshots,
                    'bid_ask_range': {'min_bid': min_bid, 'max_ask': max_ask},
                    'spread_stats': {
                        'avg': avg_spread,
                        'min': min_spread,
                        'max': max_spread
                    }
                }
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取订单簿指标失败: {e}")
            return {}
    
    def get_data_quality_score(self) -> dict:
        """计算数据质量评分"""
        quality = {}
        
        # 检查交易指标数据质量
        try:
            quality_query = """
            SELECT 
                countIf(open_price > 0) * 100.0 / count() as valid_open_pct,
                countIf(close_price > 0) * 100.0 / count() as valid_close_pct,
                countIf(total_volume > 0) * 100.0 / count() as valid_volume_pct,
                countIf(high_price >= low_price) * 100.0 / count() as valid_range_pct
            FROM trade_metrics_minutely
            """
            
            result = self.client.execute(quality_query)[0]
            valid_open_pct, valid_close_pct, valid_volume_pct, valid_range_pct = result
            
            quality['trade_metrics'] = {
                'valid_open_price': valid_open_pct,
                'valid_close_price': valid_close_pct,
                'valid_volume': valid_volume_pct,
                'valid_price_range': valid_range_pct,
                'overall_score': (valid_open_pct + valid_close_pct + valid_volume_pct + valid_range_pct) / 4
            }
            
        except Exception as e:
            logger.error(f"计算数据质量评分失败: {e}")
            quality['trade_metrics'] = {'overall_score': 0}
        
        return quality
    
    def generate_report(self) -> dict:
        """生成完整的数据摘要报告"""
        logger.info("生成数据摘要报告...")
        
        report = {
            'generated_at': datetime.now(),
            'table_summary': self.get_table_summary(),
            'trading_metrics': self.get_trading_metrics(),
            'orderbook_metrics': self.get_orderbook_metrics(),
            'data_quality': self.get_data_quality_score()
        }
        
        return report
    
    def print_report(self, report: dict):
        """打印格式化的报告"""
        print("=" * 60)
        print("金融指标系统 - 数据摘要报告")
        print("=" * 60)
        print(f"生成时间: {report['generated_at']}")
        print()
        
        # 表摘要
        print("📊 数据表摘要:")
        for table, info in report['table_summary'].items():
            print(f"  {table}:")
            print(f"    记录数: {info['count']:,}")
            if info['min_time'] and info['max_time']:
                print(f"    时间范围: {info['min_time']} 到 {info['max_time']}")
            print()
        
        # 交易指标
        print("📈 交易指标统计:")
        for symbol, metrics in report['trading_metrics'].items():
            print(f"  {symbol}:")
            print(f"    分钟数据: {metrics['minutes']} 条")
            print(f"    价格区间: {metrics['price_range']['min']:.2f} - {metrics['price_range']['max']:.2f}")
            print(f"    平均价格: {metrics['price_range']['avg']:.2f}")
            print(f"    总成交量: {metrics['volume']:,.2f}")
            print(f"    总成交额: {metrics['turnover']:,.2f}")
            print(f"    平均VWAP: {metrics['avg_vwap']:.2f}")
            print(f"    交易笔数: {metrics['total_trades']:,}")
            print()
        
        # 订单簿指标
        print("📋 订单簿统计:")
        for symbol, metrics in report['orderbook_metrics'].items():
            print(f"  {symbol}:")
            print(f"    快照数: {metrics['snapshots']} 条")
            print(f"    买卖价区间: {metrics['bid_ask_range']['min_bid']:.2f} - {metrics['bid_ask_range']['max_ask']:.2f}")
            spread_stats = metrics['spread_stats']
            print(f"    价差统计: 平均 {spread_stats['avg']:.4f}, 最小 {spread_stats['min']:.4f}, 最大 {spread_stats['max']:.4f}")
            print()
        
        # 数据质量
        print("✅ 数据质量评分:")
        for category, quality in report['data_quality'].items():
            print(f"  {category}:")
            if 'overall_score' in quality:
                print(f"    总体评分: {quality['overall_score']:.1f}%")
            for metric, score in quality.items():
                if metric != 'overall_score':
                    print(f"    {metric}: {score:.1f}%")
            print()
        
        print("=" * 60)

def main():
    """主函数"""
    generator = DataSummaryGenerator()
    
    try:
        report = generator.generate_report()
        generator.print_report(report)
        
        # 保存报告到文件
        import json
        with open('data_summary_report.json', 'w', encoding='utf-8') as f:
            # 转换datetime对象为字符串
            report_copy = report.copy()
            report_copy['generated_at'] = report_copy['generated_at'].isoformat()
            json.dump(report_copy, f, indent=2, ensure_ascii=False)
        
        print(f"📄 详细报告已保存到: data_summary_report.json")
        
    except Exception as e:
        logger.error(f"生成报告失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
