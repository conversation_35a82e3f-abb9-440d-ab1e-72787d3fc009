#!/usr/bin/env python3
"""
数据处理核心模块
处理币安交易数据，实现清洗、聚合和导入功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BinanceDataProcessor:
    """币安数据处理器"""
    
    def __init__(self, data_dir: str = "tardis_shell_data"):
        self.data_dir = Path(data_dir)
        self.trades_data = None
        self.orderbook_data = None
        
    def load_trades_data(self, filename: str) -> pd.DataFrame:
        """加载交易数据"""
        file_path = self.data_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        logger.info(f"加载交易数据: {filename}")
        
        # 读取CSV数据
        df = pd.read_csv(file_path)
        
        # 数据类型转换
        df['timestamp'] = pd.to_numeric(df['timestamp'])
        df['local_timestamp'] = pd.to_numeric(df['local_timestamp'])
        df['id'] = pd.to_numeric(df['id'])
        df['price'] = pd.to_numeric(df['price'])
        df['amount'] = pd.to_numeric(df['amount'])
        
        # 创建datetime列
        df['trade_time'] = pd.to_datetime(df['timestamp'], unit='us')
        
        # 数据验证
        self._validate_trades_data(df)
        
        logger.info(f"成功加载 {len(df)} 条交易记录")
        self.trades_data = df
        return df
    
    def _validate_trades_data(self, df: pd.DataFrame) -> None:
        """验证交易数据质量"""
        # 检查必要字段
        required_columns = ['exchange', 'symbol', 'timestamp', 'id', 'side', 'price', 'amount']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"缺少必要字段: {missing_columns}")
        
        # 检查数据范围
        if df['price'].min() <= 0:
            logger.warning("发现价格 <= 0 的记录")
        
        if df['amount'].min() <= 0:
            logger.warning("发现数量 <= 0 的记录")
        
        # 检查时间戳
        if df['timestamp'].isnull().any():
            logger.warning("发现空的时间戳")
        
        logger.info("数据验证完成")
    
    def clean_trades_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗交易数据"""
        logger.info("开始清洗交易数据...")
        
        original_count = len(df)
        
        # 移除异常数据
        df = df[df['price'] > 0]
        df = df[df['amount'] > 0]
        df = df.dropna(subset=['timestamp', 'price', 'amount'])
        
        # 移除重复记录
        df = df.drop_duplicates(subset=['id', 'timestamp'])
        
        # 排序
        df = df.sort_values(['timestamp', 'id'])
        
        cleaned_count = len(df)
        removed_count = original_count - cleaned_count
        
        logger.info(f"数据清洗完成: 原始 {original_count} 条，清洗后 {cleaned_count} 条，移除 {removed_count} 条")
        
        return df
    
    def aggregate_to_minutes(self, df: pd.DataFrame) -> pd.DataFrame:
        """聚合交易数据到分钟级别"""
        logger.info("开始聚合数据到分钟级别...")
        
        # 创建分钟级时间戳
        df['minute_time'] = df['trade_time'].dt.floor('T')
        
        # 计算成交额
        df['turnover'] = df['price'] * df['amount']
        
        # 按买卖方向分组
        df['is_buy'] = df['side'] == 'buy'
        
        # 分钟级聚合
        agg_funcs = {
            'price': ['first', 'last', 'min', 'max'],
            'amount': 'sum',
            'turnover': 'sum',
            'id': 'count'
        }
        
        # 基础聚合
        minute_data = df.groupby(['symbol', 'minute_time']).agg(agg_funcs).reset_index()
        
        # 展平列名
        minute_data.columns = ['symbol', 'metric_time', 'open_price', 'close_price', 
                              'low_price', 'high_price', 'total_volume', 'total_turnover', 'trade_count']
        
        # 计算VWAP
        minute_data['vwap'] = minute_data['total_turnover'] / minute_data['total_volume']
        
        # 计算买卖分向数据
        buy_data = df[df['is_buy']].groupby(['symbol', 'minute_time']).agg({
            'amount': 'sum',
            'turnover': 'sum'
        }).reset_index()
        buy_data.columns = ['symbol', 'metric_time', 'buy_volume', 'buy_turnover']
        
        sell_data = df[~df['is_buy']].groupby(['symbol', 'minute_time']).agg({
            'amount': 'sum', 
            'turnover': 'sum'
        }).reset_index()
        sell_data.columns = ['symbol', 'metric_time', 'sell_volume', 'sell_turnover']
        
        # 合并数据
        minute_data = minute_data.merge(buy_data, on=['symbol', 'metric_time'], how='left')
        minute_data = minute_data.merge(sell_data, on=['symbol', 'metric_time'], how='left')
        
        # 填充空值
        minute_data[['buy_volume', 'buy_turnover', 'sell_volume', 'sell_turnover']] = \
            minute_data[['buy_volume', 'buy_turnover', 'sell_volume', 'sell_turnover']].fillna(0)
        
        # 重命名symbol为contract_code
        minute_data['contract_code'] = minute_data['symbol']
        minute_data = minute_data.drop('symbol', axis=1)
        
        logger.info(f"聚合完成: 生成 {len(minute_data)} 条分钟级记录")
        
        return minute_data
    
    def prepare_for_clickhouse(self, df: pd.DataFrame, table_type: str) -> pd.DataFrame:
        """为ClickHouse导入准备数据"""
        logger.info(f"准备 {table_type} 数据用于ClickHouse导入...")
        
        if table_type == 'raw_trades':
            # 原始交易数据格式
            result = df[['exchange', 'symbol', 'timestamp', 'local_timestamp', 
                        'id', 'side', 'price', 'amount']].copy()
            result['trade_id'] = result['id']
            result = result.drop('id', axis=1)
            
        elif table_type == 'trade_metrics_minutely':
            # 分钟级指标数据格式
            result = df[['metric_time', 'contract_code', 'open_price', 'close_price',
                        'high_price', 'low_price', 'total_volume', 'total_turnover',
                        'vwap', 'trade_count', 'buy_volume', 'sell_volume', 
                        'buy_turnover', 'sell_turnover']].copy()
            
            # 确保数据类型正确
            result['trade_count'] = result['trade_count'].astype('int32')
            
        else:
            raise ValueError(f"不支持的表类型: {table_type}")
        
        # 移除空值
        result = result.dropna()
        
        logger.info(f"数据准备完成: {len(result)} 条记录")
        return result
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict:
        """获取数据摘要信息"""
        if df is None or len(df) == 0:
            return {}
        
        summary = {
            'total_records': len(df),
            'date_range': {
                'start': df['trade_time'].min() if 'trade_time' in df.columns else None,
                'end': df['trade_time'].max() if 'trade_time' in df.columns else None
            },
            'symbols': df['symbol'].unique().tolist() if 'symbol' in df.columns else [],
            'price_range': {
                'min': df['price'].min() if 'price' in df.columns else None,
                'max': df['price'].max() if 'price' in df.columns else None
            },
            'total_volume': df['amount'].sum() if 'amount' in df.columns else None
        }
        
        return summary

def main():
    """测试函数"""
    processor = BinanceDataProcessor()
    
    # 加载数据
    trades_df = processor.load_trades_data("binance-futures_trades_20250701_BTCUSDT.csv")
    
    # 数据摘要
    summary = processor.get_data_summary(trades_df)
    print("数据摘要:", summary)
    
    # 清洗数据
    clean_df = processor.clean_trades_data(trades_df)
    
    # 聚合到分钟级
    minute_df = processor.aggregate_to_minutes(clean_df)
    
    # 准备导入数据
    raw_data = processor.prepare_for_clickhouse(clean_df, 'raw_trades')
    metrics_data = processor.prepare_for_clickhouse(minute_df, 'trade_metrics_minutely')
    
    print(f"原始数据: {len(raw_data)} 条")
    print(f"分钟级数据: {len(metrics_data)} 条")

if __name__ == "__main__":
    main()
