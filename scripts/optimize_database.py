#!/usr/bin/env python3
"""
ClickHouse数据库性能优化脚本
创建索引、优化设置等
"""

import requests
import sys
from typing import Tuple

class ClickHouseOptimizer:
    def __init__(self, host="localhost", port=8123):
        self.base_url = f"http://{host}:{port}"
        
    def execute_query(self, query: str) -> Tuple[bool, str]:
        """执行ClickHouse查询"""
        try:
            response = requests.post(
                self.base_url,
                data=query,
                timeout=30
            )
            if response.status_code == 200:
                return True, response.text.strip()
            else:
                return False, f"查询失败 (HTTP {response.status_code}): {response.text}"
        except requests.exceptions.RequestException as e:
            return False, f"连接失败: {str(e)}"
    
    def create_indexes(self) -> bool:
        """创建索引以提升查询性能"""
        print("创建性能优化索引...")
        
        # 为交易指标表创建跳数索引
        index_queries = [
            """
            ALTER TABLE financial_metrics.trade_metrics_minutely 
            ADD INDEX idx_metric_time metric_time TYPE minmax GRANULARITY 1
            """,
            """
            ALTER TABLE financial_metrics.trade_metrics_minutely 
            ADD INDEX idx_contract_code contract_code TYPE bloom_filter GRANULARITY 1
            """,
            """
            ALTER TABLE financial_metrics.raw_trades 
            ADD INDEX idx_trade_time trade_time TYPE minmax GRANULARITY 1
            """,
            """
            ALTER TABLE financial_metrics.raw_trades 
            ADD INDEX idx_symbol symbol TYPE bloom_filter GRANULARITY 1
            """
        ]
        
        for query in index_queries:
            success, result = self.execute_query(query)
            if not success and "already exists" not in result:
                print(f"✗ 索引创建失败: {result}")
                return False
        
        print("✓ 性能索引创建成功")
        return True
    
    def optimize_settings(self) -> bool:
        """优化数据库设置"""
        print("优化数据库设置...")
        
        # 设置查询优化参数
        settings = [
            "SET max_memory_usage = 10000000000",  # 10GB内存限制
            "SET max_execution_time = 300",        # 5分钟执行时间限制
            "SET max_threads = 8",                 # 最大线程数
            "SET optimize_on_insert = 1"           # 插入时自动优化
        ]
        
        for setting in settings:
            success, result = self.execute_query(setting)
            if not success:
                print(f"✗ 设置失败: {setting} - {result}")
                return False
        
        print("✓ 数据库设置优化完成")
        return True
    
    def analyze_tables(self) -> bool:
        """分析表统计信息"""
        print("分析表统计信息...")
        
        tables = [
            "financial_metrics.raw_trades",
            "financial_metrics.trade_metrics_minutely",
            "financial_metrics.orderbook_snapshots"
        ]
        
        for table in tables:
            # 获取表大小信息
            size_query = f"""
            SELECT 
                formatReadableSize(sum(bytes)) as size,
                count() as parts,
                sum(rows) as rows
            FROM system.parts 
            WHERE database = 'financial_metrics' 
            AND table = '{table.split('.')[1]}'
            AND active = 1
            """
            
            success, result = self.execute_query(size_query)
            if success and result:
                print(f"  {table}: {result}")
            else:
                print(f"  {table}: 暂无数据")
        
        print("✓ 表统计信息分析完成")
        return True
    
    def create_materialized_views(self) -> bool:
        """创建物化视图以提升查询性能"""
        print("创建物化视图...")
        
        # 创建小时级聚合物化视图
        hourly_mv = """
        CREATE MATERIALIZED VIEW IF NOT EXISTS financial_metrics.mv_hourly_metrics
        ENGINE = SummingMergeTree()
        PARTITION BY toYYYYMM(hour_time)
        ORDER BY (contract_code, hour_time)
        AS SELECT
            toStartOfHour(metric_time) as hour_time,
            contract_code,
            argMin(open_price, metric_time) as open_price,
            argMax(close_price, metric_time) as close_price,
            max(high_price) as high_price,
            min(low_price) as low_price,
            sum(total_volume) as total_volume,
            sum(total_turnover) as total_turnover,
            sum(trade_count) as trade_count
        FROM financial_metrics.trade_metrics_minutely
        GROUP BY hour_time, contract_code
        """
        
        success, result = self.execute_query(hourly_mv)
        if not success and "already exists" not in result:
            print(f"✗ 小时级物化视图创建失败: {result}")
            return False
        
        print("✓ 物化视图创建成功")
        return True

def main():
    """主函数"""
    print("=== ClickHouse数据库性能优化 ===\n")
    
    optimizer = ClickHouseOptimizer()
    
    # 测试连接
    print("测试ClickHouse连接...")
    success, result = optimizer.execute_query("SELECT 1")
    if not success:
        print(f"✗ ClickHouse连接失败: {result}")
        sys.exit(1)
    print("✓ ClickHouse连接正常\n")
    
    # 执行优化步骤
    steps = [
        optimizer.create_indexes,
        optimizer.optimize_settings,
        optimizer.create_materialized_views,
        optimizer.analyze_tables
    ]
    
    for step in steps:
        if not step():
            print("\n✗ 数据库优化失败")
            sys.exit(1)
        print()
    
    print("✓ 数据库性能优化完成！")

if __name__ == "__main__":
    main()
