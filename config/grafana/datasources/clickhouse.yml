apiVersion: 1

datasources:
  - name: ClickHouse-Financial
    type: grafana-clickhouse-datasource
    access: proxy
    url: http://clickhouse:8123
    database: financial_metrics
    user: admin
    secureJsonData:
      password: admin123
    jsonData:
      defaultDatabase: financial_metrics
      dialTimeout: 10
      queryTimeout: 60
      idleTimeout: 60
      httpHeaders:
        - name: X-ClickHouse-User
          value: admin
      settings:
        - name: max_execution_time
          value: "60"
        - name: max_memory_usage
          value: "10000000000"
    isDefault: true
    editable: true
