#!/bin/bash

# 金融指标系统本地安装脚本
# 适用于macOS系统

echo "=== 金融指标系统本地安装 ==="

# 检查Homebrew
if ! command -v brew &> /dev/null; then
    echo "错误: 需要安装Homebrew"
    echo "请访问 https://brew.sh/ 安装Homebrew"
    exit 1
fi

# 安装ClickHouse
echo "正在安装ClickHouse..."
if ! command -v clickhouse &> /dev/null; then
    brew install clickhouse
else
    echo "ClickHouse已安装"
fi

# 安装Grafana
echo "正在安装Grafana..."
if ! command -v grafana-server &> /dev/null; then
    brew install grafana
else
    echo "Grafana已安装"
fi

# 创建ClickHouse配置目录
echo "配置ClickHouse..."
CLICKHOUSE_CONFIG_DIR="/opt/homebrew/etc/clickhouse-server"
if [ ! -d "$CLICKHOUSE_CONFIG_DIR" ]; then
    sudo mkdir -p "$CLICKHOUSE_CONFIG_DIR"
fi

# 复制ClickHouse配置
if [ -f "config/clickhouse/custom.xml" ]; then
    sudo cp config/clickhouse/custom.xml "$CLICKHOUSE_CONFIG_DIR/"
    echo "ClickHouse配置已复制"
fi

# 启动ClickHouse服务
echo "启动ClickHouse服务..."
brew services start clickhouse

# 等待ClickHouse启动
echo "等待ClickHouse启动..."
sleep 5

# 测试ClickHouse连接
echo "测试ClickHouse连接..."
if curl -s "http://localhost:8123/ping" > /dev/null; then
    echo "✓ ClickHouse连接成功"
else
    echo "✗ ClickHouse连接失败"
fi

# 启动Grafana服务
echo "启动Grafana服务..."
brew services start grafana

# 等待Grafana启动
echo "等待Grafana启动..."
sleep 10

# 测试Grafana连接
echo "测试Grafana连接..."
if curl -s "http://localhost:3000/api/health" > /dev/null; then
    echo "✓ Grafana连接成功"
else
    echo "✗ Grafana连接失败"
fi

echo ""
echo "=== 安装完成 ==="
echo "ClickHouse: http://localhost:8123"
echo "Grafana: http://localhost:3000 (admin/admin)"
echo ""
echo "下一步:"
echo "1. 运行 python scripts/setup_database.py 创建数据库"
echo "2. 运行 python scripts/import_data.py 导入数据"
echo "3. 在Grafana中配置ClickHouse数据源"
