# Grafana仪表盘使用指南

## 概述

本系统提供了两个专业的金融数据可视化仪表盘，用于分析BTCUSDT交易数据。

## 访问信息

- **Grafana地址**: http://localhost:3000
- **用户名**: admin
- **密码**: admin123

## 仪表盘列表

### 1. 金融交易指标仪表盘
**用途**: 基础交易数据监控和分析

**包含图表**:
- **价格走势图**: 显示BTCUSDT收盘价时间序列
- **成交量图**: 展示总成交量、买入量、卖出量
- **K线图(OHLC)**: 蜡烛图显示开高低收价格
- **交易统计**: 平均价格、总成交量、总成交额、交易笔数
- **买卖价差**: 订单簿价差和中间价分析

### 2. BTCUSDT技术分析仪表盘
**用途**: 专业技术分析和深度市场洞察

**包含图表**:
- **K线图 + 移动平均线**: 收盘价、MA20、MA50移动平均线
- **成交量分析**: 成交量分布和买入占比
- **VWAP分析**: VWAP vs 收盘价对比和偏离度
- **价格波动分析**: 价格区间、波动率、涨跌幅
- **订单簿分析**: 最优买卖价、价差、价差基点
- **实时统计**: 关键指标的实时数值

## 功能特性

### 时间控制
- **时间范围选择**: 支持自定义时间范围查询
- **自动刷新**: 30秒/1分钟自动刷新数据
- **时区设置**: Asia/Shanghai时区显示

### 交互功能
- **缩放**: 鼠标滚轮缩放图表
- **平移**: 拖拽查看不同时间段
- **图例控制**: 点击图例显示/隐藏数据系列
- **工具提示**: 悬停显示详细数值

### 数据源
- **实时数据**: 连接ClickHouse实时查询
- **高性能**: 优化的SQL查询确保快速响应
- **数据完整性**: 100%数据质量保证

## 技术指标说明

### 移动平均线 (MA)
- **MA20**: 20分钟移动平均线，短期趋势指标
- **MA50**: 50分钟移动平均线，中期趋势指标
- **用途**: 判断价格趋势方向和支撑阻力位

### 成交量加权平均价 (VWAP)
- **计算**: 成交额 ÷ 成交量
- **用途**: 衡量平均成交价格，判断价格是否偏离合理区间
- **偏离度**: (收盘价 - VWAP) / VWAP × 100%

### 价格波动率
- **计算**: (最高价 - 最低价) / 收盘价 × 100%
- **用途**: 衡量价格波动程度，评估市场活跃度

### 买卖价差
- **价差**: 最优卖价 - 最优买价
- **价差基点**: 价差 / 中间价 × 10000
- **用途**: 衡量市场流动性，价差越小流动性越好

## 使用技巧

### 1. 趋势分析
- 观察收盘价与移动平均线的关系
- 价格在MA20和MA50之上通常表示上升趋势
- 移动平均线的排列可以判断趋势强度

### 2. 成交量分析
- 价格上涨配合成交量放大是健康的上涨信号
- 买入占比超过50%表示买盘较强
- 成交量萎缩可能预示趋势转换

### 3. VWAP策略
- 价格高于VWAP表示当前价格偏高
- 价格低于VWAP表示当前价格偏低
- 偏离度过大时可能出现回归

### 4. 波动率监控
- 高波动率时期需要谨慎交易
- 低波动率可能预示大行情来临
- 结合成交量判断波动率的有效性

## 自定义查询

### 修改时间范围
```sql
-- 查询最近1小时数据
AND metric_time >= now() - INTERVAL 1 HOUR

-- 查询特定日期
AND metric_time >= '2025-07-01 08:00:00'
AND metric_time <= '2025-07-01 16:00:00'
```

### 添加新指标
```sql
-- 计算RSI指标（简化版）
SELECT 
    metric_time,
    close_price,
    avg(close_price) OVER (
        ORDER BY metric_time 
        ROWS BETWEEN 13 PRECEDING AND CURRENT ROW
    ) as RSI_MA14
FROM financial_metrics.trade_metrics_minutely
```

### 多时间框架分析
```sql
-- 5分钟K线聚合
SELECT 
    toStartOfInterval(metric_time, INTERVAL 5 MINUTE) as time_5m,
    argMin(open_price, metric_time) as open_5m,
    argMax(close_price, metric_time) as close_5m,
    max(high_price) as high_5m,
    min(low_price) as low_5m,
    sum(total_volume) as volume_5m
FROM financial_metrics.trade_metrics_minutely
GROUP BY time_5m
ORDER BY time_5m
```

## 性能优化

### 查询优化建议
1. **限制时间范围**: 避免查询过长时间段
2. **使用索引**: 查询条件包含时间和合约代码
3. **合理聚合**: 大时间范围时使用聚合查询
4. **缓存利用**: 重复查询会利用ClickHouse缓存

### 刷新频率设置
- **实时监控**: 30秒刷新
- **历史分析**: 5分钟或手动刷新
- **长期分析**: 关闭自动刷新

## 故障排除

### 常见问题

1. **图表无数据显示**
   - 检查时间范围是否有数据
   - 确认ClickHouse服务正常
   - 验证数据源连接状态

2. **查询超时**
   - 缩小时间范围
   - 检查ClickHouse性能
   - 优化查询语句

3. **图表显示异常**
   - 刷新浏览器页面
   - 检查Grafana插件状态
   - 重启Grafana服务

### 日志查看
```bash
# Grafana日志
docker logs financial-grafana

# ClickHouse日志
docker logs financial-clickhouse
```

## 扩展功能

### 添加告警
1. 在Grafana中配置告警规则
2. 设置价格、成交量等阈值
3. 配置通知渠道（邮件、钉钉等）

### 导出数据
1. 使用Grafana的导出功能
2. 直接查询ClickHouse获取原始数据
3. 通过API接口获取图表数据

### 自定义面板
1. 添加新的查询语句
2. 选择合适的可视化类型
3. 配置图表样式和交互选项
