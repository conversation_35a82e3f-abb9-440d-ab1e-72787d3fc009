# 数据处理指南

## 概述

本指南介绍如何使用Python脚本处理币安交易数据并导入到ClickHouse数据库。

## 脚本说明

### 1. data_processor.py - 数据处理核心模块

**功能**：
- 加载和验证CSV格式的交易数据
- 数据清洗和质量检查
- 聚合原始交易数据到分钟级指标
- 为ClickHouse导入准备数据格式

**主要类**：
```python
class BinanceDataProcessor:
    def load_trades_data(filename)      # 加载交易数据
    def clean_trades_data(df)           # 清洗数据
    def aggregate_to_minutes(df)        # 聚合到分钟级
    def prepare_for_clickhouse(df, type) # 准备导入格式
```

### 2. clickhouse_importer.py - ClickHouse导入模块

**功能**：
- 连接ClickHouse数据库
- 批量插入数据
- 数据格式验证
- 导入结果验证

**主要类**：
```python
class ClickHouseImporter:
    def insert_dataframe(df, table)     # 插入数据
    def verify_import(table, count)     # 验证导入
    def optimize_table(table)           # 优化表性能
```

### 3. import_data.py - 完整导入流水线

**功能**：
- 端到端的数据导入流程
- 支持批量处理多个文件
- 错误处理和统计报告

**使用方法**：
```bash
# 导入所有数据
python3 scripts/import_data.py

# 清空现有数据后导入
python3 scripts/import_data.py --clear

# 只导入分钟级指标数据
python3 scripts/import_data.py --no-raw

# 指定文件模式
python3 scripts/import_data.py --pattern "*BTCUSDT*.csv"
```

### 4. validate_data.py - 数据验证脚本

**功能**：
- 验证数据质量和完整性
- 检查数据一致性
- 生成验证报告

## 数据处理流程

### 步骤1：原始数据加载
```python
processor = BinanceDataProcessor()
trades_df = processor.load_trades_data("binance-futures_trades_20250701_BTCUSDT.csv")
```

### 步骤2：数据清洗
```python
clean_df = processor.clean_trades_data(trades_df)
```
- 移除价格或数量 <= 0 的记录
- 移除重复交易
- 按时间戳排序

### 步骤3：分钟级聚合
```python
minute_df = processor.aggregate_to_minutes(clean_df)
```
生成的指标包括：
- OHLC价格数据
- 成交量和成交额
- VWAP（成交量加权平均价）
- 买卖分向统计
- 交易笔数

### 步骤4：数据导入
```python
importer = ClickHouseImporter()
importer.insert_dataframe(minute_df, 'financial_metrics.trade_metrics_minutely')
```

## 数据格式说明

### 输入格式（CSV）
```csv
exchange,symbol,timestamp,local_timestamp,id,side,price,amount
binance-futures,BTCUSDT,1751328000018000,1751328000021428,6440230568,sell,107087.3,0.002
```

### 输出格式（分钟级指标）
| 字段 | 类型 | 说明 |
|------|------|------|
| metric_time | DateTime | 分钟级时间戳 |
| contract_code | String | 合约代码 |
| open_price | Float64 | 开盘价 |
| close_price | Float64 | 收盘价 |
| high_price | Float64 | 最高价 |
| low_price | Float64 | 最低价 |
| total_volume | Float64 | 总成交量 |
| total_turnover | Float64 | 总成交额 |
| vwap | Float64 | 成交量加权平均价 |
| trade_count | UInt32 | 交易笔数 |
| buy_volume | Float64 | 买入成交量 |
| sell_volume | Float64 | 卖出成交量 |
| buy_turnover | Float64 | 买入成交额 |
| sell_turnover | Float64 | 卖出成交额 |

## 性能优化

### 批量导入
- 默认批次大小：10,000条记录
- 支持进度条显示
- 自动错误重试

### 内存管理
- 分块处理大文件
- 及时释放DataFrame内存
- 优化数据类型

### ClickHouse优化
- 使用MergeTree引擎
- 按月分区存储
- 创建适当索引

## 错误处理

### 常见错误及解决方案

1. **连接失败**
   ```
   错误：ClickHouse连接失败
   解决：检查ClickHouse服务是否启动
   ```

2. **数据格式错误**
   ```
   错误：列数量不匹配
   解决：检查CSV文件格式是否正确
   ```

3. **内存不足**
   ```
   错误：内存溢出
   解决：减小批次大小或增加系统内存
   ```

## 监控和维护

### 数据质量监控
```bash
# 运行数据验证
python3 scripts/validate_data.py

# 检查表统计信息
clickhouse client --query "SELECT count() FROM financial_metrics.trade_metrics_minutely"
```

### 性能监控
```sql
-- 查看查询性能
SELECT * FROM system.query_log 
WHERE type = 'QueryFinish' 
ORDER BY event_time DESC 
LIMIT 10;

-- 查看表大小
SELECT 
    database,
    table,
    formatReadableSize(sum(bytes)) as size,
    sum(rows) as rows
FROM system.parts 
WHERE database = 'financial_metrics'
GROUP BY database, table;
```

## 最佳实践

1. **数据备份**：定期备份重要数据分区
2. **增量更新**：支持增量数据导入
3. **错误日志**：保留详细的处理日志
4. **性能测试**：定期进行性能基准测试
5. **数据验证**：每次导入后验证数据质量

## 扩展功能

### 支持更多数据源
- 修改`BinanceDataProcessor`类
- 添加新的数据格式解析器
- 扩展数据验证规则

### 实时数据处理
- 集成Kafka消息队列
- 实现流式数据处理
- 添加实时监控告警
