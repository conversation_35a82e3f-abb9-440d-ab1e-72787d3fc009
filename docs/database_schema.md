# ClickHouse数据库设计文档

## 数据库概述

**数据库名称**: `financial_metrics`  
**用途**: 金融交易数据存储和指标计算  
**引擎**: MergeTree系列  
**分区策略**: 按月分区 (YYYYMM)  

## 表结构设计

### 1. 原始交易数据表 (raw_trades)

存储从交易所获取的原始交易记录。

```sql
CREATE TABLE financial_metrics.raw_trades (
    exchange String COMMENT '交易所名称',
    symbol String COMMENT '交易对符号', 
    timestamp UInt64 COMMENT '交易时间戳(微秒)',
    local_timestamp UInt64 COMMENT '本地时间戳(微秒)',
    trade_id UInt64 COMMENT '交易ID',
    side Enum8('buy' = 1, 'sell' = 2) COMMENT '买卖方向',
    price Float64 COMMENT '交易价格',
    amount Float64 COMMENT '交易数量',
    trade_time DateTime64(6) MATERIALIZED fromUnixTimestamp64Micro(timestamp) COMMENT '交易时间'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_time)
ORDER BY (symbol, trade_time, trade_id)
```

**关键特性**:
- 使用微秒级时间戳确保精度
- 物化列`trade_time`便于时间查询
- 按交易对和时间排序优化查询性能

### 2. 交易指标表 (trade_metrics_minutely)

存储分钟级聚合的交易指标数据。

```sql
CREATE TABLE financial_metrics.trade_metrics_minutely (
    metric_time DateTime COMMENT '指标计算时间(精确到分钟)',
    contract_code String COMMENT '合约代码',
    open_price Float64 COMMENT '开盘价',
    close_price Float64 COMMENT '收盘价', 
    high_price Float64 COMMENT '最高价',
    low_price Float64 COMMENT '最低价',
    total_volume Float64 COMMENT '总成交量',
    total_turnover Float64 COMMENT '总成交额',
    vwap Float64 COMMENT '成交量加权平均价',
    trade_count UInt32 COMMENT '交易笔数',
    buy_volume Float64 COMMENT '买入成交量',
    sell_volume Float64 COMMENT '卖出成交量', 
    buy_turnover Float64 COMMENT '买入成交额',
    sell_turnover Float64 COMMENT '卖出成交额'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(metric_time)
ORDER BY (contract_code, metric_time)
```

**关键特性**:
- OHLC价格数据支持K线图绘制
- 分买卖方向的成交量统计
- VWAP等技术指标预计算

### 3. 订单簿快照表 (orderbook_snapshots)

存储订单簿的快照数据。

```sql
CREATE TABLE financial_metrics.orderbook_snapshots (
    exchange String COMMENT '交易所名称',
    symbol String COMMENT '交易对符号',
    timestamp UInt64 COMMENT '快照时间戳(微秒)',
    local_timestamp UInt64 COMMENT '本地时间戳(微秒)',
    snapshot_time DateTime64(6) MATERIALIZED fromUnixTimestamp64Micro(timestamp) COMMENT '快照时间',
    asks Array(Tuple(Float64, Float64)) COMMENT '卖盘数据[(价格,数量)]',
    bids Array(Tuple(Float64, Float64)) COMMENT '买盘数据[(价格,数量)]',
    best_ask Float64 COMMENT '最优卖价',
    best_bid Float64 COMMENT '最优买价', 
    spread Float64 COMMENT '买卖价差',
    mid_price Float64 COMMENT '中间价'
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(snapshot_time)
ORDER BY (symbol, snapshot_time)
```

**关键特性**:
- 数组类型存储多档位数据
- 预计算关键指标(价差、中间价)
- 支持深度分析和流动性研究

## 物化视图

### 小时级聚合视图 (mv_hourly_metrics)

自动聚合分钟数据到小时级别。

```sql
CREATE MATERIALIZED VIEW financial_metrics.mv_hourly_metrics
ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(hour_time)
ORDER BY (contract_code, hour_time)
AS SELECT
    toStartOfHour(metric_time) as hour_time,
    contract_code,
    argMin(open_price, metric_time) as open_price,
    argMax(close_price, metric_time) as close_price,
    max(high_price) as high_price,
    min(low_price) as low_price,
    sum(total_volume) as total_volume,
    sum(total_turnover) as total_turnover,
    sum(trade_count) as trade_count
FROM financial_metrics.trade_metrics_minutely
GROUP BY hour_time, contract_code
```

## 性能优化

### 索引策略

1. **跳数索引**: 为时间字段创建minmax索引
2. **布隆过滤器**: 为字符串字段创建bloom_filter索引
3. **排序键优化**: 按查询模式设计ORDER BY

### 分区策略

- **按月分区**: `PARTITION BY toYYYYMM(time_field)`
- **优势**: 
  - 支持高效的时间范围查询
  - 便于数据生命周期管理
  - 优化并行查询性能

### 压缩配置

- **默认压缩**: LZ4算法
- **存储优化**: 列式存储减少I/O
- **内存使用**: 合理设置缓存大小

## 查询示例

### 基础查询

```sql
-- 查询某个时间段的交易指标
SELECT * FROM financial_metrics.trade_metrics_minutely 
WHERE contract_code = 'BTCUSDT' 
AND metric_time >= '2025-07-01 00:00:00'
AND metric_time < '2025-07-02 00:00:00'
ORDER BY metric_time;

-- 计算日级OHLC
SELECT 
    toDate(metric_time) as trade_date,
    contract_code,
    argMin(open_price, metric_time) as open_price,
    argMax(close_price, metric_time) as close_price,
    max(high_price) as high_price,
    min(low_price) as low_price,
    sum(total_volume) as volume
FROM financial_metrics.trade_metrics_minutely
WHERE contract_code = 'BTCUSDT'
GROUP BY trade_date, contract_code
ORDER BY trade_date;
```

### 高级分析

```sql
-- 计算移动平均价格
SELECT 
    metric_time,
    close_price,
    avg(close_price) OVER (
        PARTITION BY contract_code 
        ORDER BY metric_time 
        ROWS BETWEEN 19 PRECEDING AND CURRENT ROW
    ) as ma20
FROM financial_metrics.trade_metrics_minutely
WHERE contract_code = 'BTCUSDT'
ORDER BY metric_time;
```

## 维护建议

1. **定期优化**: 运行`OPTIMIZE TABLE`合并数据分片
2. **监控性能**: 查看`system.query_log`分析慢查询
3. **清理数据**: 设置TTL策略自动清理历史数据
4. **备份策略**: 定期备份重要数据分区
