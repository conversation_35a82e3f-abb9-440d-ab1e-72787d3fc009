# 金融指标系统 - ClickHouse + Grafana

基于ClickHouse和Grafana的金融交易数据分析和可视化系统。

## 系统架构

- **ClickHouse**: 高性能时间序列数据库，存储和计算金融指标
- **Grafana**: 数据可视化平台，展示交易指标仪表盘
- **Python ETL**: 数据处理脚本，处理币安交易数据

## 快速开始

### 1. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 2. 访问服务

- **ClickHouse HTTP接口**: http://localhost:8123
- **Grafana仪表盘**: http://localhost:3000
  - 用户名: admin
  - 密码: admin123

### 3. 验证连接

```bash
# 测试ClickHouse连接
curl "http://localhost:8123/ping"

# 执行简单查询
curl "http://localhost:8123/?query=SELECT version()"
```

## 目录结构

```
├── docker-compose.yml          # Docker服务编排配置
├── config/                     # 配置文件目录
│   ├── clickhouse/            # ClickHouse配置
│   └── grafana/               # Grafana配置
├── data/                      # 数据持久化目录
│   ├── clickhouse/            # ClickHouse数据
│   └── grafana/               # Grafana数据
├── scripts/                   # Python脚本目录
├── grafana-dashboards/        # Grafana仪表盘配置
├── logs/                      # 日志文件目录
└── tardis_shell_data/         # 原始交易数据
```

## 数据处理流程

1. **原始数据**: 币安期货交易数据（CSV格式）
2. **ETL处理**: Python脚本清洗和聚合数据
3. **数据存储**: ClickHouse存储分钟级交易指标
4. **数据可视化**: Grafana展示交易分析图表

## 下一步

1. 创建ClickHouse数据库和表结构
2. 开发Python数据处理脚本
3. 设计Grafana可视化仪表盘
4. 导入和分析交易数据

## 故障排除

### 常见问题

1. **端口冲突**: 确保8123、9000、3000端口未被占用
2. **权限问题**: 确保data目录有写入权限
3. **内存不足**: 调整Docker内存限制

### 日志查看

```bash
# ClickHouse日志
docker-compose logs clickhouse

# Grafana日志
docker-compose logs grafana
```
