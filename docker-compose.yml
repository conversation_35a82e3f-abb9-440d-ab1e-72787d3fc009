version: '3.8'

services:
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: financial-clickhouse
    hostname: clickhouse
    ports:
      - "8123:8123"  # HTTP接口
      - "9000:9000"  # Native接口
    volumes:
      - ./data/clickhouse:/var/lib/clickhouse
      - ./config/clickhouse:/etc/clickhouse-server/config.d
      - ./logs/clickhouse:/var/log/clickhouse-server
    environment:
      CLICKHOUSE_DB: financial_metrics
      CLICKHOUSE_USER: admin
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: 1
      CLICKHOUSE_PASSWORD: admin123
    ulimits:
      nofile:
        soft: 262144
        hard: 262144
    networks:
      - financial-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:9.5.0-ubuntu
    container_name: financial-grafana
    hostname: grafana
    ports:
      - "3000:3000"
    volumes:
      - ./data/grafana:/var/lib/grafana
      - ./grafana-dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana:/etc/grafana/provisioning
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-clickhouse-datasource
      GF_USERS_ALLOW_SIGN_UP: false
      GF_USERS_ALLOW_ORG_CREATE: false
      GF_USERS_AUTO_ASSIGN_ORG: true
      GF_USERS_AUTO_ASSIGN_ORG_ROLE: Viewer
    networks:
      - financial-network
    restart: unless-stopped
    depends_on:
      - clickhouse

networks:
  financial-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  clickhouse-data:
    driver: local
  grafana-data:
    driver: local
